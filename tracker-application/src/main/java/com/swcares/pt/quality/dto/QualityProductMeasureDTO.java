package com.swcares.pt.quality.dto;

import java.time.LocalDate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.quality.dto.QualityProductMeasureDTO <br>
 * Description：产品质量度量数据传输对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="QualityProductMeasureDTO", description="产品质量度量数据传输对象")
public class QualityProductMeasureDTO {

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "统计月份")
    private LocalDate statisticMonth;

    @ApiModelProperty(value = "统计时间")
    private LocalDate statisticDate;

    @ApiModelProperty(value = "sonar分析时间")
    private LocalDate sonarDate;

    @ApiModelProperty(value = "安全评级")
    private String securityRating;

    @ApiModelProperty(value = "安全问题数量")
    private String securityIssues;

    @ApiModelProperty(value = "可靠性评级")
    private String reliabilityRating;

    @ApiModelProperty(value = "可维护性问题数量")
    private String reliabilityIssues;

    @ApiModelProperty(value = "新代码可维护性评级")
    private String newMaintainabilityRating;

    @ApiModelProperty(value = "可维护性问题数量")
    private String maintainabilityIssues;

    @ApiModelProperty(value = "重复代码行密度")
    private String duplicatedLinesDensity;

    @ApiModelProperty(value = "代码覆盖率")
    private String coverage;

    @ApiModelProperty(value = "非注释代码行数")
    private String ncloc;

    @ApiModelProperty(value = "年代码增量")
    private String annualIncrement;

}
