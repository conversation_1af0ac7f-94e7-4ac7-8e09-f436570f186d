package com.swcares.pt.quality.dto;

import java.time.LocalDate;

import com.swcares.baseframe.common.base.dto.BasePagedDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO <br>
 * Description：产品质量度量分页查询数据传输对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="QualityProductMeasurePagedDTO", description="产品质量度量分页查询数据传输对象")
public class QualityProductMeasurePagedDTO extends BasePagedDTO {

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "统计月份")
    private LocalDate statisticMonth;

    @ApiModelProperty(value = "统计时间")
    private LocalDate statisticDate;

    @ApiModelProperty(value = "sonar分析时间")
    private LocalDate sonarDate;

    @ApiModelProperty(value = "安全评级")
    private String securityRating;

    @ApiModelProperty(value = "可靠性评级")
    private String reliabilityRating;

    @ApiModelProperty(value = "新代码可维护性评级")
    private String newMaintainabilityRating;

}
