# 产品质量模块开发文档

## 概述

产品质量模块用于管理和跟踪产品的质量度量数据，包括代码质量、安全性、可靠性等指标。

## 数据库表结构

### quality_product_measure 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| product_code | varchar(32) | 产品编码 |
| statistic_month | date | 统计月份 |
| statistic_date | date | 统计时间 |
| sonar_date | date | sonar分析时间 |
| security_rating | varchar(4) | 安全评级 |
| security_issues | varchar(64) | 安全问题数量 |
| reliability_rating | varchar(4) | 可靠性评级 |
| reliability_issues | varchar(64) | 可维护性问题数量 |
| new_maintainability_rating | varchar(4) | 新代码可维护性评级 |
| maintainability_issues | varchar(64) | 可维护性问题数量 |
| duplicated_lines_density | varchar(4) | 重复代码行密度 |
| coverage | varchar(8) | 代码覆盖率 |
| ncloc | varchar(32) | 非注释代码行数 |
| annual_increment | varchar(32) | 年代码增量 |
| created_by | varchar(32) | 创建人 |
| created_time | datetime | 创建时间 |
| updated_by | varchar(32) | 更新人 |
| updated_time | datetime | 更新时间 |
| deleted | tinyint(1) | 删除状态 |

## 模块结构

### 1. 数据层 (Infrastructure)
- **QualityProductMeasureDO**: 数据实体类
- **QualityProductMeasureMapper**: MyBatis Mapper接口
- **QualityProductMeasureMapper.xml**: SQL映射文件
- **QualityProductMeasureService**: 服务接口
- **QualityProductMeasureServiceImpl**: 服务实现类
- **QualityProductMeasureRepositoryImpl**: 仓储实现类

### 2. 应用层 (Application)
- **QualityProductMeasureDTO**: 数据传输对象
- **QualityProductMeasurePagedDTO**: 分页查询DTO
- **QualityProductMeasureAppService**: 应用服务

### 3. 聚合层 (Aggregation)
- **QualityProductMeasure**: 领域实体
- **QualityProductMeasureRepository**: 仓储接口

### 4. 接口层 (Interfaces)
- **QualityProductMeasureVO**: 视图对象
- **QualityProductMeasureController**: REST控制器
- **QualityProductMeasureDOConverter**: 对象转换器

## API 接口

### 1. 分页查询产品质量度量
```
POST /quality/product-measure/page
```

请求体示例：
```json
{
  "productCode": "PROD001",
  "statisticMonth": "2025-07-01",
  "pageNum": 1,
  "pageSize": 10
}
```

### 2. 查询产品质量度量列表
```
POST /quality/product-measure/list
```

请求体示例：
```json
{
  "productCode": "PROD001",
  "securityRating": "A"
}
```

### 3. 查询产品最新质量度量
```
GET /quality/product-measure/latest?productCode=PROD001
```

## 使用示例

### 1. 查询指定产品的质量度量数据
```java
@Autowired
private QualityProductMeasureAppService qualityProductMeasureAppService;

public void queryProductQuality(String productCode) {
    QualityProductMeasureDTO dto = QualityProductMeasureDTO.builder()
            .productCode(productCode)
            .build();
    
    List<QualityProductMeasureVO> list = qualityProductMeasureAppService.getByCondition(dto);
    // 处理查询结果
}
```

### 2. 分页查询质量度量数据
```java
public void queryProductQualityPage() {
    QualityProductMeasurePagedDTO dto = QualityProductMeasurePagedDTO.builder()
            .productCode("PROD001")
            .pageNum(1)
            .pageSize(10)
            .build();
    
    IPage<QualityProductMeasureVO> page = qualityProductMeasureAppService.page(dto);
    // 处理分页结果
}
```

## 部署说明

1. 执行 `docs/sql/quality_product_measure.sql` 创建数据库表
2. 确保项目依赖正确配置
3. 启动应用，访问 Swagger 文档查看API详情

## 注意事项

1. 所有查询默认过滤已删除的记录 (deleted = 0)
2. 查询结果按统计时间倒序排列
3. 产品编码为必填字段
4. 支持按多个条件组合查询

## 扩展功能

后续可以考虑添加以下功能：
1. 质量趋势分析
2. 质量报告生成
3. 质量预警机制
4. 质量数据导入导出
