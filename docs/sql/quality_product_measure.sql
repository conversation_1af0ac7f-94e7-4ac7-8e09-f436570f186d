-- 产品质量度量表
CREATE TABLE `quality_product_measure` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `product_code` varchar(32) NOT NULL COMMENT '产品编码',
  `statistic_month` date DEFAULT NULL COMMENT '统计月份',
  `statistic_date` date DEFAULT NULL COMMENT '统计时间',
  `sonar_date` date DEFAULT NULL COMMENT 'sonar分析时间',
  `security_rating` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '安全评级',
  `security_issues` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '安全问题数量',
  `reliability_rating` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '可靠性评级',
  `reliability_issues` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '可维护性问题数量',
  `new_maintainability_rating` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '新代码可维护性评级',
  `maintainability_issues` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '可维护性问题数量',
  `duplicated_lines_density` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '重复代码行密度',
  `coverage` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代码覆盖率',
  `ncloc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '非注释代码行数',
  `annual_increment` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '年代码增量',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除状态(1-删除,0-未删除)',
  PRIMARY KEY (`id`),
  KEY `idx_product_code` (`product_code`),
  KEY `idx_statistic_date` (`statistic_date`),
  KEY `idx_statistic_month` (`statistic_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品质量度量表';

-- 插入示例数据
INSERT INTO `quality_product_measure` (
  `product_code`, 
  `statistic_month`, 
  `statistic_date`, 
  `sonar_date`, 
  `security_rating`, 
  `security_issues`, 
  `reliability_rating`, 
  `reliability_issues`, 
  `new_maintainability_rating`, 
  `maintainability_issues`, 
  `duplicated_lines_density`, 
  `coverage`, 
  `ncloc`, 
  `annual_increment`, 
  `created_by`
) VALUES 
(
  'PROD001', 
  '2025-07-01', 
  '2025-07-02', 
  '2025-07-02', 
  'A', 
  '0', 
  'A', 
  '5', 
  'A', 
  '10', 
  '2.5', 
  '85.6', 
  '15000', 
  '2000', 
  'admin'
),
(
  'PROD002', 
  '2025-07-01', 
  '2025-07-02', 
  '2025-07-02', 
  'B', 
  '2', 
  'B', 
  '8', 
  'B', 
  '15', 
  '3.2', 
  '78.9', 
  '12000', 
  '1500', 
  'admin'
);
