package com.swcares.pt.quality.controller;

import java.time.LocalDate;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.pt.quality.QualityProductMeasureAppService;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;

/**
 * ClassName：com.swcares.pt.quality.controller.QualityProductMeasureControllerTest <br>
 * Description：产品质量度量控制器测试类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@SpringBootTest
public class QualityProductMeasureControllerTest {

    @Autowired
    private QualityProductMeasureAppService qualityProductMeasureAppService;

    @Test
    public void testPage() {
        QualityProductMeasurePagedDTO dto = QualityProductMeasurePagedDTO.builder()
                .productCode("PROD001")
                .pageNum(1)
                .pageSize(10)
                .build();
        
        IPage<QualityProductMeasureVO> page = qualityProductMeasureAppService.page(dto);
        System.out.println("Total: " + page.getTotal());
        System.out.println("Records: " + page.getRecords().size());
    }

    @Test
    public void testList() {
        QualityProductMeasureDTO dto = QualityProductMeasureDTO.builder()
                .productCode("PROD001")
                .statisticMonth(LocalDate.of(2025, 7, 1))
                .build();
        
        List<QualityProductMeasureVO> list = qualityProductMeasureAppService.getByCondition(dto);
        System.out.println("List size: " + list.size());
        
        if (!list.isEmpty()) {
            QualityProductMeasureVO first = list.get(0);
            System.out.println("Product Code: " + first.getProductCode());
            System.out.println("Security Rating: " + first.getSecurityRating());
            System.out.println("Coverage: " + first.getCoverage());
        }
    }

}
