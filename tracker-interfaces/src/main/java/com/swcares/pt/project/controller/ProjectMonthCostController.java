package com.swcares.pt.project.controller;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.project.ProjectMonthCostAppService;
import com.swcares.pt.project.assembler.MonthCostAssembler;
import com.swcares.pt.project.bo.ProjectMonthCostBO;
import com.swcares.pt.project.converter.MonthCostDOConverter;
import com.swcares.pt.project.domain.ProjectMonthCost;
import com.swcares.pt.project.dto.ProjectMonthCostDTO;
import com.swcares.pt.project.dto.ProjectMonthCostPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthCostDO;
import com.swcares.pt.project.service.ProjectMonthCostService;
import com.swcares.pt.project.vo.ProjectMonthCostVO;
import com.swcares.pt.util.ExcelUtil;
import com.swcares.pt.util.UserUtil;
import com.swcares.pt.util.ValidateUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectMonthCostController <br>
 * Description：项目月成本 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/cost")
@Api(tags = "项目月成本接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectMonthCostController extends BaseController {
    
	@Autowired
    private ProjectMonthCostService monthCostService;
	@Autowired
	private ProjectMonthCostAppService monthCostAppService;

    @PostMapping("/save")
    @ApiOperation(value = "新建项目月成本记录")
    public BaseResult<Object> save(@RequestBody ProjectMonthCostDTO dto) {
    	ProjectMonthCost cost = MonthCostAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!cost.save()) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除项目月成本记录")
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
        boolean deleted = monthCostService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改项目月成本记录")
    public BaseResult<Object> update(@RequestBody ProjectMonthCostDTO dto) {
    	ProjectMonthCost cost = MonthCostAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!cost.save()) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目月成本记录")
    public BaseResult<ProjectMonthCostVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectMonthCostDO cost = monthCostService.getById(id);
        return ok(MonthCostDOConverter.INSTANCE.toVO(cost));
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目月成本记录")
    public PagedResult<List<ProjectMonthCostVO>> page(@RequestBody ProjectMonthCostPagedDTO dto) {
        IPage<ProjectMonthCostVO> result = monthCostService.page(dto);
        return ok(result);
    }
    
    @PostMapping("/importData")
    @ApiOperation(value = "导入数据")
    public BaseResult<Object> importData(@RequestPart MultipartFile file) throws IOException {
        List<ProjectMonthCostBO> temps = ExcelUtil.readDatas(file, ProjectMonthCostBO.class);
        
        List<ProjectMonthCostDTO> datas = MonthCostAssembler.INSTANCE.toDTOs(temps);
        ValidateUtil.validate(datas, Boolean.TRUE);
        monthCostAppService.importDatas(MonthCostAssembler.INSTANCE.toDomains(datas, UserUtil.getUser()));
        return ok();
    }
    
}
