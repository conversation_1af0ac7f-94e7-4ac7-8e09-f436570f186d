package com.swcares.pt.project.controller;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.common.rl.core.annotation.RateLimiter;
import com.swcares.components.log.annotation.Log;
import com.swcares.components.log.enums.BusinessType;
import com.swcares.components.log.enums.OperatorType;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.core.aspect.DataScope;
import com.swcares.pt.enums.DataTemplate;
import com.swcares.pt.project.ProjectMilestoneAppService;
import com.swcares.pt.project.assembler.MilestoneAssembler;
import com.swcares.pt.project.converter.MilestoneDOConverter;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.context.PreMilestoneContext;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.dto.ProjectMilestonePagedDTO;
import com.swcares.pt.project.entity.ProjectMilestoneDO;
import com.swcares.pt.project.service.ProjectMilestoneService;
import com.swcares.pt.project.vo.MilestoneExportVO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;
import com.swcares.pt.util.ExcelUtil;
import com.swcares.pt.util.ToolUtil;
import com.swcares.pt.util.Tools;
import com.swcares.pt.util.UserUtil;
import com.swcares.pt.util.ValidateUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectMilestoneController <br>
 * Description：项目里程碑 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-12 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/milestone")
@Api(tags = "项目里程碑接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectMilestoneController extends BaseController {
	
    @Autowired
    private ProjectMilestoneService milestoneService;
    @Autowired
    private ProjectMilestoneAppService milestoneAppService;
    
    @PostMapping("/save")
    @ApiOperation(value = "新建项目里程碑记录")
    @Log(title = "添加里程碑", operatorType = OperatorType.WEB, businessType = BusinessType.INSERT)
    public BaseResult<Object> save(@RequestBody ProjectMilestoneDTO dto) {
    	ProjectMilestone pm = MilestoneAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!pm.save()) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除项目里程碑记录")
    @Log(title = "删除里程碑", operatorType = OperatorType.WEB, businessType = BusinessType.DELETE)
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
    	ProjectMilestoneDO pm = milestoneService.getById(id);
        boolean deleted = milestoneAppService.deleteMilstone(MilestoneDOConverter.INSTANCE.fromDO(pm));
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改项目里程碑记录")
    @Log(title = "修改里程碑", operatorType = OperatorType.WEB, businessType = BusinessType.UPDATE)
    public BaseResult<Object> update(@RequestBody ProjectMilestoneDTO dto) {
    	ProjectMilestone pm = MilestoneAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!pm.save()) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目里程碑记录")
    public BaseResult<ProjectMilestoneVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectMilestoneDO pm = milestoneService.getById(id);
        return ok(MilestoneDOConverter.INSTANCE.toVO(pm));
    }

    @DataScope
    @PostMapping("/page")
    @RateLimiter()
    @ApiOperation(value = "条件分页查询项目里程碑记录")
    public PagedResult<List<ProjectMilestoneVO>> page(@RequestBody ProjectMilestonePagedDTO dto) {
        IPage<ProjectMilestoneVO> result = milestoneService.page(dto);
        return ok(result);
    }
    
    @DataScope
    @PostMapping("/export")
    @ApiOperation(value = "数据导出")
    @Log(title = "里程碑导出", operatorType = OperatorType.WEB, businessType = BusinessType.EXPORT)
    public void exportData(@RequestBody ProjectMilestonePagedDTO dto, HttpServletResponse response) throws IOException {
    	dto.setPageSize(1000000L);
    	IPage<ProjectMilestoneVO> datas = milestoneService.page(dto);
    	List<MilestoneExportVO> expData = MilestoneAssembler.INSTANCE.toExportVOs(datas.getRecords());
    	ExcelUtil.export(response, expData, "项目里程碑-"+ LdtUtils.dtNow(), "项目里程碑数据", "项目里程碑", MilestoneExportVO.class);
    }
    
    @PostMapping("/importData")
    @ApiOperation(value = "导入数据")
    @Log(title = "里程碑导入", operatorType = OperatorType.WEB, businessType = BusinessType.IMPORT)
    public BaseResult<Object> importData(@RequestPart MultipartFile file) throws IOException {
    	if(file == null) {
    		throw new BusinessException(CommonErrors.CUSTOM_ERROR, "数据文件不能为空！");
    	}
        List<ProjectMilestoneDTO> datas = ExcelUtil.readDatas(file, ProjectMilestoneDTO.class);
        ValidateUtil.validate(datas, Boolean.TRUE);
        List<ProjectMilestone> source = MilestoneAssembler.INSTANCE.toDomains(datas, UserUtil.getUser());
        PreMilestoneContext ctx = PreMilestoneContext.builder()
        		.source(source).hoildays(Tools.loadHolidays())
        		.costPrice(Tools.loadCostPrice()).build();
        milestoneAppService.importDatas(ctx);
        return ok();
    }
    
    @PostMapping("/template")
    @ApiOperation(value = "数据模板下载")
    public void template(HttpServletRequest req, HttpServletResponse res) {
    	ToolUtil.downTemplate(req, res, DataTemplate.Milestone);
    }
    
}
