package com.swcares.pt.project.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.project.converter.ProjectYearStatDOConverter;
import com.swcares.pt.project.dto.ProjectYearStatPagedDTO;
import com.swcares.pt.project.entity.ProjectYearStatDO;
import com.swcares.pt.project.service.ProjectYearStatService;
import com.swcares.pt.project.vo.ProjectYearStatVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectYearStatController <br>
 * Description：项目年统计 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/yearStat")
@Api(tags = "项目年统计接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectYearStatController extends BaseController {
	
    @Autowired
    private ProjectYearStatService yearStatService;

//    @PostMapping("/save")
//    @ApiOperation(value = "新建项目年统计记录")
//    public BaseResult<Object> save(@RequestBody ProjectYearStatDTO dto) {
//    	ProjectYearStat stat = ProjectYearStatAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!stat.save()) {
//            throw new BusinessException(CommonErrors.CREATE_ERROR);
//        }
//        return ok();
//    }

//    @PostMapping("/delete/{id}")
//    @ApiOperation(value = "通过ID删除项目年统计记录")
//    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
//        boolean deleted = yearStatService.logicRemoveById(id);
//        if (!deleted) {
//            throw new BusinessException(CommonErrors.DELETE_ERROR);
//        }
//        return ok();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改项目年统计记录")
//    public BaseResult<Object> update(@RequestBody ProjectYearStatDTO dto) {
//    	ProjectYearStat stat = ProjectYearStatAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!stat.save()) {
//            throw new BusinessException(CommonErrors.UPDATE_ERROR);
//        }
//        return ok();
//    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目年统计记录")
    public BaseResult<ProjectYearStatVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectYearStatDO stat = yearStatService.getById(id);
        return ok(ProjectYearStatDOConverter.INSTANCE.toVO(stat));
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目年统计记录")
    public PagedResult<List<ProjectYearStatVO>> page(@RequestBody ProjectYearStatPagedDTO dto) {
        IPage<ProjectYearStatVO> result = yearStatService.page(dto);
        return ok(result);
    }
    
}
