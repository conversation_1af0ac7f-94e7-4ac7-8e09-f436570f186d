package com.swcares.pt.project.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.core.aspect.DataScope;
import com.swcares.pt.project.converter.MonthPlanDOConverter;
import com.swcares.pt.project.dto.ProjectMonthPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthPlanDO;
import com.swcares.pt.project.service.ProjectMonthPlanService;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectMonthPlanController <br>
 * Description：项目月计划 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/plan")
@Api(tags = "项目月计划接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectMonthPlanController extends BaseController {
	
    @Autowired
    private ProjectMonthPlanService monthPlanService;
//    @Autowired
//    private ProjectMonthPlanAppService monthPlanAppService;

//    @PostMapping("/save")
//    @ApiOperation(value = "新建项目月计划记录")
//    public BaseResult<Object> save(@RequestBody ProjectMonthPlanDTO dto) {
//    	ProjectMonthPlan plan = MonthPlanAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!plan.save()) {
//            throw new BusinessException(CommonErrors.CREATE_ERROR);
//        }
//        return ok();
//    }

//    @PostMapping("/delete/{id}")
//    @ApiOperation(value = "通过ID删除项目月计划记录")
//    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
//        boolean deleted = monthPlanService.logicRemoveById(id);
//        if (!deleted) {
//            throw new BusinessException(CommonErrors.DELETE_ERROR);
//        }
//        return ok();
//    }

//    @PostMapping("/update")
//    @ApiOperation(value = "修改项目月计划记录")
//    public BaseResult<Object> update(@RequestBody ProjectMonthPlanDTO dto) {
//    	ProjectMonthPlan plan = MonthPlanAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!plan.save()) {
//            throw new BusinessException(CommonErrors.UPDATE_ERROR);
//        }
//        return ok();
//    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目月计划记录")
    public BaseResult<ProjectMonthPlanVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectMonthPlanDO plan = monthPlanService.getById(id);
        return ok(MonthPlanDOConverter.INSTANCE.toVO(plan));
    }

    @DataScope
    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目月计划记录")
    public PagedResult<List<ProjectMonthPlanVO>> page(@RequestBody ProjectMonthPlanPagedDTO dto) {
        IPage<ProjectMonthPlanVO> result = monthPlanService.page(dto);
        return ok(result);
    }
    
//    @PostMapping("/importData")
//    @ApiOperation(value = "导入数据")
//    public BaseResult<Object> importData(@RequestPart MultipartFile file) throws IOException {
//        List<ProjectMonthPlanDTO> datas = ExcelUtil.readDatas(file, ProjectMonthPlanDTO.class);
//        ValidateUtil.validate(datas, Boolean.TRUE);
//        monthPlanAppService.importDatas(MonthPlanAssembler.INSTANCE.toDomains(datas, UserUtil.getUser()));
//        return ok();
//    }
    
}
