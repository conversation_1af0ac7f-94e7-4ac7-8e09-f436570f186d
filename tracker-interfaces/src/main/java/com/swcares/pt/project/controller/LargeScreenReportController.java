/**  
 * All rights Reserved, Designed By <br>
 * Title：LargeScreenReportController.java <br>
 * Package：com.swcares.pt.project.controller <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午8:59:12 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.pt.base.vo.StaffLargeScreenVO;
import com.swcares.pt.base.vo.StaffLoadAnalysisVO;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.query.ProjectLargeScreenQuery;
import com.swcares.pt.project.query.StaffLargeScreenQuery;
import com.swcares.pt.project.vo.ManagementUnitRiskStatVO;
import com.swcares.pt.project.vo.ManagementUnitStatVO;
import com.swcares.pt.project.vo.ProjectCostPaceStatVO;
import com.swcares.pt.project.vo.ProjectLargeScreenVO;
import com.swcares.pt.util.ToolUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**   
 * ClassName：com.swcares.pt.project.controller.LargeScreenReportController <br>
 * Description：大屏展示控制器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午8:59:12 <br>
 * @version v1.0 <br>  
 */
@RestController
@RequestMapping("/pls")
@Api(tags = "大屏接口")
@ApiVersion(value = PmtApiVer.PMT_PLS_API)
public class LargeScreenReportController extends BaseController{

	@Autowired
	private ProjectLargeScreenQuery largeScreenQuery;
	@Autowired
	private StaffLargeScreenQuery staffLargeScreenQuery;
	@Autowired
	private PasswordEncoder passwordEncoder;
	
	@GetMapping("/pow")
    @ApiOperation(value = "项目总览")
    public BaseResult<ProjectLargeScreenVO> projectOverview(ProjectInfoParam params) {
		ProjectLargeScreenVO pows = largeScreenQuery.searchProjectOverview(params);
		return ok(pows);
	}
	
	@GetMapping("/pcs")
    @ApiOperation(value = "项目进度/成本统计")
    public BaseResult<ProjectCostPaceStatVO> projectPaceCostStat(LargeScreenParam params) {
		String p = passwordEncoder.encode("123456");
		System.out.println(p);
		boolean matches = passwordEncoder.matches("$2a$10$ES9j.fYwfSyPXKebHwQc9.D52YCQxoVcYKgjWUxB0/MH8Ts2PkOt6", p);
		System.out.println(matches);
		ProjectCostPaceStatVO pcs = largeScreenQuery.searchCostPaceStat(params);
		return ok(pcs);
	}
	
	@GetMapping("/mut_pace")
    @ApiOperation(value = "经营单元进度符合度")
    public BaseResult<List<ManagementUnitStatVO>> mgrUnitPace(LargeScreenParam params) {
		List<ManagementUnitStatVO> pows = largeScreenQuery.searchMgrUnitPaceStat(params);
		ToolUtil.mappingMgtUnit(pows);
		return ok(pows);
	}
	
	@GetMapping("/mut_cost")
    @ApiOperation(value = "经营单元成本利用率")
    public BaseResult<List<ManagementUnitStatVO>> mgrUnitCost(LargeScreenParam params) {
		List<ManagementUnitStatVO> pows = largeScreenQuery.searchMgrUnitCostStat(params);
		ToolUtil.mappingMgtUnit(pows);
		return ok(pows);
	}
	
	@GetMapping("/mut_risk")
    @ApiOperation(value = "经营单元成本/进度风险")
    public BaseResult<List<ManagementUnitRiskStatVO>> mgrUnitRisk(LargeScreenParam params) {
		List<ManagementUnitRiskStatVO> risks = largeScreenQuery.queryProjectRiskStat(params);
		return ok(risks);
	}
	
	@GetMapping("/staff_load")
    @ApiOperation(value = "资源负载分析，包含负载度、利用率数据")
    public BaseResult<List<StaffLoadAnalysisVO>> staffLoad(LargeScreenParam params) {
		List<StaffLoadAnalysisVO> loads = staffLargeScreenQuery.queryStaffAnalysis(params);
		return ok(loads);
	}
	
	@GetMapping("/staff_ow")
    @ApiOperation(value = "资源总览")
    public BaseResult<StaffLargeScreenVO> staffOverview(LargeScreenParam params) {
		StaffLargeScreenVO ls = staffLargeScreenQuery.searchStaffOverview(params);
		return ok(ls);
	}
	
}
