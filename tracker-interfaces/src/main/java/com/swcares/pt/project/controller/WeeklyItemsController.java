package com.swcares.pt.project.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.pt.common.cons.PmtApiVer;

import io.swagger.annotations.Api;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.WeeklyItemsController <br>
 * Description：周报项 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/weekly/item")
@Api(tags = "周报项接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class WeeklyItemsController extends BaseController {
	
//    @Autowired
//    private WeeklyItemsService weeklyItemsService;
//
//    @PostMapping("/save")
//    @ApiOperation(value = "新建周报项记录")
//    public BaseResult<Object> save(@RequestBody WeeklyItemsDTO dto) {
//    	WeeklyItems prt = WeeklyItemsAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!prt.save()) {
//            throw new BusinessException(CommonErrors.CREATE_ERROR);
//        }
//        return ok();
//    }
//
//    @PostMapping("/delete/{id}")
//    @ApiOperation(value = "通过ID删除周报项记录")
//    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
//        boolean deleted = weeklyItemsService.logicRemoveById(id);
//        if (!deleted) {
//            throw new BusinessException(CommonErrors.DELETE_ERROR);
//        }
//        return ok();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改周报项记录")
//    public BaseResult<Object> update(@RequestBody WeeklyItemsDTO dto) {
//    	WeeklyItems prt = WeeklyItemsAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!prt.save()) {
//            throw new BusinessException(CommonErrors.UPDATE_ERROR);
//        }
//        return ok();
//    }
//
//    @GetMapping("/get")
//    @ApiOperation(value = "通过ID查询周报项记录")
//    public BaseResult<WeeklyItemsVO> get(@ApiParam(value = "主键id", required = true) Long id) {
//        WeeklyItemsDO item = weeklyItemsService.getById(id);
//        return ok(WeeklyItemsDOConverter.INSTANCE.toVO(item));
//    }
//
//    @PostMapping("/page")
//    @ApiOperation(value = "条件分页查询周报项记录")
//    public PagedResult<List<WeeklyItemsVO>> page(@RequestBody WeeklyItemsPagedDTO dto) {
//        IPage<WeeklyItemsVO> result = weeklyItemsService.page(dto);
//        return ok(result);
//    }
    
}
