package com.swcares.pt.project.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.components.log.annotation.Log;
import com.swcares.components.log.enums.BusinessType;
import com.swcares.components.log.enums.OperatorType;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.enums.RiskStateEnum;
import com.swcares.pt.project.assembler.RiskTrackAssembler;
import com.swcares.pt.project.converter.RiskTrackDOConverter;
import com.swcares.pt.project.domain.ProjectRiskTrack;
import com.swcares.pt.project.dto.ProjectRiskTrackDTO;
import com.swcares.pt.project.dto.ProjectRiskTrackPagedDTO;
import com.swcares.pt.project.entity.ProjectRiskTrackDO;
import com.swcares.pt.project.service.ProjectRiskTrackService;
import com.swcares.pt.project.vo.ProjectRiskTrackVO;
import com.swcares.pt.util.UserUtil;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectRiskTrackController <br>
 * Description：项目风险 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/risk")
@Api(tags = "项目风险接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectRiskTrackController extends BaseController {
	
    @Autowired
    private ProjectRiskTrackService riskTrackService;

    @PostMapping("/save")
    @ApiOperation(value = "新建项目风险记录")
    @Log(title = "保存风险记录", operatorType = OperatorType.WEB, businessType = BusinessType.INSERT)
    public BaseResult<Object> save(@RequestBody ProjectRiskTrackDTO dto) {
    	if(StrUtil.isEmpty(dto.getProjectCode())) {
    		throw new BusinessException(CommonErrors.CUSTOM_ERROR, "项目编号不能为空！");
    	}
    	ProjectRiskTrack prt = RiskTrackAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!prt.save()) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
    
    @PostMapping("/solve")
    @ApiOperation(value = "项目风险解决")
    @Log(title = "解决风险", operatorType = OperatorType.WEB, businessType = BusinessType.UPDATE)
    public BaseResult<Object> solve(@ApiParam(value = "主键id", required = true) @Valid @RequestBody ProjectRiskTrackDTO dto) {
    	dto.setRiskState(RiskStateEnum.ok.getCode());
    	ProjectRiskTrack prt = RiskTrackAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!prt.save()) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
    
    @PostMapping("/close")
    @ApiOperation(value = "项目风险关闭")
    @Log(title = "关闭风险", operatorType = OperatorType.WEB, businessType = BusinessType.UPDATE)
    public BaseResult<Object> close(@ApiParam(value = "主键id", required = true) @Valid @RequestBody ProjectRiskTrackDTO dto) {
    	dto.setRiskState(RiskStateEnum.closed.getCode());
    	dto.setCloseDate(LdtUtils.now());
    	ProjectRiskTrack prt = RiskTrackAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
    	if (!prt.save()) {
    		throw new BusinessException(CommonErrors.CREATE_ERROR);
    	}
    	return ok();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除项目风险记录")
    @Log(title = "删除风险", operatorType = OperatorType.WEB, businessType = BusinessType.DELETE)
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
        boolean deleted = riskTrackService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目风险记录")
    public BaseResult<ProjectRiskTrackVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectRiskTrackDO prt = riskTrackService.getById(id);
        return ok(RiskTrackDOConverter.INSTANCE.toVO(prt));
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目风险记录")
    public PagedResult<List<ProjectRiskTrackVO>> page(@Valid @RequestBody ProjectRiskTrackPagedDTO dto) {
        IPage<ProjectRiskTrackVO> result = riskTrackService.page(dto);
        return ok(result);
    }
    
}
