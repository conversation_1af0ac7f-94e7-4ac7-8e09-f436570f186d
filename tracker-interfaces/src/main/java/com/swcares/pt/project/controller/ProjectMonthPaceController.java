package com.swcares.pt.project.controller;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.components.log.annotation.Log;
import com.swcares.components.log.enums.BusinessType;
import com.swcares.components.log.enums.OperatorType;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.core.aspect.DataScope;
import com.swcares.pt.project.assembler.MonthPaceAssembler;
import com.swcares.pt.project.converter.MonthPaceDOConverter;
import com.swcares.pt.project.dto.ProjectMonthPacePagedDTO;
import com.swcares.pt.project.entity.ProjectMonthPaceDO;
import com.swcares.pt.project.service.ProjectMonthPaceService;
import com.swcares.pt.project.vo.MonthPaceExportVO;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;
import com.swcares.pt.util.ExcelUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectMonthPaceController <br>
 * Description：项目月进度 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/pace")
@Api(tags = "项目月进度接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectMonthPaceController extends BaseController {
    
	@Autowired
    private ProjectMonthPaceService monthPaceService;

//    @PostMapping("/save")
//    @ApiOperation(value = "新建项目月进度记录")
//    public BaseResult<Object> save(@RequestBody ProjectMonthPaceDTO dto) {
//    	ProjectMonthPace pace = MonthPaceAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!pace.save()) {
//            throw new BusinessException(CommonErrors.CREATE_ERROR);
//        }
//        return ok();
//    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除项目月进度记录")
    @Log(title = "删除月进度", operatorType = OperatorType.WEB, businessType = BusinessType.DELETE)
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
        boolean deleted = monthPaceService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

//    @PostMapping("/update")
//    @ApiOperation(value = "修改项目月进度记录")
//    public BaseResult<Object> update(@RequestBody ProjectMonthPaceDTO dto) {
//    	ProjectMonthPace pace = MonthPaceAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!pace.save()) {
//            throw new BusinessException(CommonErrors.UPDATE_ERROR);
//        }
//        return ok();
//    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目月进度记录")
    public BaseResult<ProjectMonthPaceVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectMonthPaceDO pace = monthPaceService.getById(id);
        return ok(MonthPaceDOConverter.INSTANCE.toVO(pace));
    }

    @DataScope
    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目月进度记录")
    public PagedResult<List<ProjectMonthPaceVO>> page(@RequestBody ProjectMonthPacePagedDTO dto) {
        IPage<ProjectMonthPaceVO> result = monthPaceService.page(dto);
        return ok(result);
    }
    
    @DataScope
    @PostMapping("/export")
    @ApiOperation(value = "导出数据")
    @Log(title = "导出月进度", operatorType = OperatorType.WEB, businessType = BusinessType.EXPORT)
    public void exportData(@RequestBody ProjectMonthPacePagedDTO dto, HttpServletResponse response) throws IOException {
    	dto.setPageSize(1000000L);
    	IPage<ProjectMonthPaceVO> datas = monthPaceService.page(dto);
    	List<MonthPaceExportVO> expData = MonthPaceAssembler.INSTANCE.toExportVOs(datas.getRecords());
    	ExcelUtil.export(response, expData, "项目月度进展-"+ LdtUtils.dtNow(), "项目月度进展", MonthPaceExportVO.class);
    }
    
}
