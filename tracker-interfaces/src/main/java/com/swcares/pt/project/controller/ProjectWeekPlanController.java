package com.swcares.pt.project.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.project.converter.WeekPlanDOConverter;
import com.swcares.pt.project.dto.ProjectWeekPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectWeekPlanDO;
import com.swcares.pt.project.service.ProjectWeekPlanService;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectWeekPlanController <br>
 * Description：项目周计划 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/week/plan")
@Api(tags = "项目周计划接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectWeekPlanController extends BaseController {
	
    @Autowired
    private ProjectWeekPlanService weekPlanService;

//    @PostMapping("/save")
//    @ApiOperation(value = "新建项目周计划记录")
//    public BaseResult<Object> save(@RequestBody ProjectWeekPlanDTO dto) {
//    	ProjectWeekPlan prt = WeekPlanAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!prt.save()) {
//            throw new BusinessException(CommonErrors.CREATE_ERROR);
//        }
//        return ok();
//    }

//    @PostMapping("/delete/{id}")
//    @ApiOperation(value = "通过ID删除项目周计划记录")
//    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
//        boolean deleted = weekPlanService.logicRemoveById(id);
//        if (!deleted) {
//            throw new BusinessException(CommonErrors.DELETE_ERROR);
//        }
//        return ok();
//    }

//    @PostMapping("/update")
//    @ApiOperation(value = "修改项目周计划记录")
//    public BaseResult<Object> update(@RequestBody ProjectWeekPlanDTO dto) {
//    	ProjectWeekPlan prt = WeekPlanAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
//        if (!prt.save()) {
//            throw new BusinessException(CommonErrors.UPDATE_ERROR);
//        }
//        return ok();
//    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目周计划记录")
    public BaseResult<ProjectWeekPlanVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectWeekPlanDO wp = weekPlanService.getById(id);
        return ok(WeekPlanDOConverter.INSTANCE.toVO(wp));
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目周计划记录")
    public PagedResult<List<ProjectWeekPlanVO>> page(@RequestBody ProjectWeekPlanPagedDTO dto) {
        IPage<ProjectWeekPlanVO> result = weekPlanService.page(dto);
        return ok(result);
    }
    
}
