package com.swcares.pt.project.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.common.enums.EmpStatusEnum;
import com.swcares.common.rl.core.annotation.RateLimiter;
import com.swcares.common.uc.dto.EmployeePagedDTO;
import com.swcares.common.uc.service.EmployeeService;
import com.swcares.common.uc.vo.EmployeeVO;
import com.swcares.components.log.annotation.Log;
import com.swcares.components.log.enums.BusinessType;
import com.swcares.components.log.enums.OperatorType;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.core.aspect.DataScope;
import com.swcares.pt.enums.DataTemplate;
import com.swcares.pt.project.ProjectInfoAppService;
import com.swcares.pt.project.assembler.ProjectInfoAssembler;
import com.swcares.pt.project.bo.ProjectInfoBO;
import com.swcares.pt.project.converter.ProjectInfoDOConverter;
import com.swcares.pt.project.domain.ProjectInfo;
import com.swcares.pt.project.dto.ProjectInfoDTO;
import com.swcares.pt.project.dto.ProjectInfoPagedDTO;
import com.swcares.pt.project.entity.ProjectInfoDO;
import com.swcares.pt.project.service.ProjectInfoService;
import com.swcares.pt.project.vo.ProjectInfoVO;
import com.swcares.pt.util.ExcelUtil;
import com.swcares.pt.util.ToolUtil;
import com.swcares.pt.util.UserUtil;
import com.swcares.pt.util.ValidateUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectInfoController <br>
 * Description：项目信息 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/pi")
@Api(tags = "项目信息接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectInfoController extends BaseController {
	
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private ProjectInfoAppService projectInfoAppService;
    @Autowired
    private EmployeeService employeeService;

    @PostMapping("/save")
    @ApiOperation(value = "新建项目信息记录")
    @Log(title = "添加项目", operatorType = OperatorType.WEB, businessType = BusinessType.INSERT)
    public BaseResult<Object> save(@RequestBody ProjectInfoDTO dto) {
    	ProjectInfo p = ProjectInfoAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!p.save()) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除项目信息记录")
    @Log(title = "删除项目", operatorType = OperatorType.WEB, businessType = BusinessType.DELETE)
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) @PathVariable("id") Long id) {
        boolean deleted = projectInfoAppService.removeProject(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改项目信息记录")
    @Log(title = "修改项目", operatorType = OperatorType.WEB, businessType = BusinessType.UPDATE)
    public BaseResult<Object> update(@RequestBody ProjectInfoDTO dto) {
    	ProjectInfo p = ProjectInfoAssembler.INSTANCE.toDomain(dto, UserUtil.getUser());
        if (!p.save()) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目信息记录")
    public BaseResult<ProjectInfoVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ProjectInfoDO projectInfo = projectInfoService.getById(id);
        return ok(ProjectInfoDOConverter.INSTANCE.toVO(projectInfo));
    } 

    @DataScope
    @RateLimiter()
    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目信息记录")
    public PagedResult<List<ProjectInfoVO>> page(@RequestBody ProjectInfoPagedDTO dto) {
        IPage<ProjectInfoVO> result = projectInfoService.page(dto);
        return ok(result);
    }
    
    @PostMapping("/importData")
    @ApiOperation(value = "导入数据")
    @Log(title = "导入项目", operatorType = OperatorType.WEB, businessType = BusinessType.IMPORT)
    public BaseResult<Object> importData(@RequestPart MultipartFile file) throws IOException {
    	if(file == null) {
    		throw new BusinessException(CommonErrors.CUSTOM_ERROR, "数据文件不能为空！");
    	}
        List<ProjectInfoBO> temps = ExcelUtil.readDatas(file, ProjectInfoBO.class);
        List<ProjectInfoDTO> datas = ProjectInfoAssembler.INSTANCE.toDTOs(temps);
        ValidateUtil.validate(datas, Boolean.TRUE);
        valifyJobNumber(datas);
        projectInfoAppService.importDatas(ProjectInfoAssembler.INSTANCE.toDomains(datas, UserUtil.getUser()));
        return ok();
    }
    
    @PostMapping("/template")
    @ApiOperation(value = "数据模板下载")
    public void template(HttpServletRequest req, HttpServletResponse res) {
    	ToolUtil.downTemplate(req, res, DataTemplate.Project);
    }
    
    private void valifyJobNumber(List<ProjectInfoDTO> datas) {
    	Set<String> jobNumbers = datas.stream().map(ProjectInfoDTO::getJobNumber).collect(Collectors.toSet());
    	EmployeePagedDTO params = new EmployeePagedDTO();
    	params.setStatus(EmpStatusEnum.WORKING.getCode());
    	params.setPageSize(100000000);
    	IPage<EmployeeVO> page = employeeService.page(params);
    	List<EmployeeVO> records = page.getRecords();
    	Map<String, EmployeeVO> emps = records.stream().collect(Collectors.toMap(EmployeeVO::getJobNumber, m -> m));
    	for (String job : jobNumbers) {
    		EmployeeVO emp = emps.get(job.trim());
    		if(emp == null) {
    			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "工号【"+ job + "】未在用户中心数据中，请核对");
    		}
		}
    }
    
}
