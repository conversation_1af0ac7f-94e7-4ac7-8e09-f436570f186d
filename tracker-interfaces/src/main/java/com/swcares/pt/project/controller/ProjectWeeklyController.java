package com.swcares.pt.project.controller;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.common.rl.core.annotation.RepeatSubmit;
import com.swcares.components.log.annotation.Log;
import com.swcares.components.log.enums.BusinessType;
import com.swcares.components.log.enums.OperatorType;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.core.aspect.DataScope;
import com.swcares.pt.project.ProjectWeeklyAppService;
import com.swcares.pt.project.assembler.WeeklyAssembler;
import com.swcares.pt.project.dto.ProjectWeeklyDTO;
import com.swcares.pt.project.dto.ProjectWeeklyPagedDTO;
import com.swcares.pt.project.param.ProjectWeeklyParam;
import com.swcares.pt.project.service.ProjectWeeklyService;
import com.swcares.pt.project.vo.ProjectWeeklyDetailVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;
import com.swcares.pt.project.vo.WeeklyExportVO;
import com.swcares.pt.util.ExcelUtil;
import com.swcares.pt.util.Tools;
import com.swcares.pt.util.UserUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.pt.project.pmt.controller.ProjectWeeklyController <br>
 * Description：项目周报 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/plm/weekly")
@Api(tags = "项目周报接口")
@ApiVersion(value = PmtApiVer.PMT_PLM_API)
public class ProjectWeeklyController extends BaseController {
	
    @Autowired
    private ProjectWeeklyService weeklyService;
    @Autowired
    private ProjectWeeklyAppService weeklyAppService;

    @DataScope
    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询项目周报记录")
    public PagedResult<List<ProjectWeeklyVO>> page(@RequestBody ProjectWeeklyPagedDTO dto) {
        IPage<ProjectWeeklyVO> result = weeklyService.page(dto);
        return ok(result);
    }
    
    @RepeatSubmit(interval = 10, timeUnit = TimeUnit.SECONDS)
    @PostMapping("/fill_in")
    @ApiOperation(value = "填报项目周报")
    @Log(title = "填写周报", operatorType = OperatorType.WEB, businessType = BusinessType.UPDATE)
    public BaseResult<Object> save(@Valid @RequestBody ProjectWeeklyDTO dto) {
        if (!weeklyAppService.fillIn(dto, Tools.loadOffsetThreshold(), UserUtil.getUser())) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
    
    @PostMapping("/generate")
    @ApiOperation(value = "生成项目周报", notes = "可提供参数weeklyDate或year,yearWeekNum生成指定日期的周报，默认生成当前日期所在周的周报。")
    public BaseResult<Object> create(@RequestBody ProjectWeeklyParam params) {
    	params.setUser(UserUtil.getUser());
    	weeklyAppService.generateWeekly(params);
        return ok();
    }

    @RepeatSubmit(interval = 10, timeUnit = TimeUnit.SECONDS)
    @PostMapping("/track")
    @ApiOperation(value = "项目周报跟踪")
    @Log(title = "周报跟踪", operatorType = OperatorType.WEB, businessType = BusinessType.UPDATE)
    public BaseResult<Object> track(@Valid @RequestBody ProjectWeeklyDTO dto) {
        if (!weeklyAppService.track(dto, Tools.loadOffsetThreshold(), UserUtil.getUser())) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }
    
    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询项目周报记录")
    public BaseResult<ProjectWeeklyDetailVO> get(@ApiParam(value = "主键id", required = true) Long id) {
    	ProjectWeeklyDetailVO wly = weeklyService.getByWeeklyId(id);
        return ok(wly);
    }

    @GetMapping("/load")
    @ApiOperation(value = "填报通过ID加载项目周报，同时校验上周周报填写状态")
    public BaseResult<ProjectWeeklyDetailVO> load(@ApiParam(value = "主键id", required = true) Long id) {
    	ProjectWeeklyDetailVO wly = weeklyAppService.loadWeekly(id);
        return ok(wly);
    }
    
    @DataScope
    @PostMapping("/export")
    @ApiOperation(value = "数据导出")
    @Log(title = "周报导出", operatorType = OperatorType.WEB, businessType = BusinessType.EXPORT)
    public void exportData(@RequestBody ProjectWeeklyPagedDTO dto, HttpServletResponse response) throws IOException {
    	dto.setPageSize(1000000L);
    	IPage<ProjectWeeklyVO> result = weeklyService.page(dto);
    	List<WeeklyExportVO> datas = WeeklyAssembler.INSTANCE.toExportVOs(result.getRecords());
    	ExcelUtil.export(response, datas, "项目周报-"+ LdtUtils.dtNow(), "项目周报数据", "项目周报", WeeklyExportVO.class);
    }

}
