package com.swcares.pt.quality.vo;

import java.time.LocalDate;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.quality.vo.QualityProductMeasureVO <br>
 * Description：产品质量度量视图对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="QualityProductMeasureVO", description="产品质量度量视图对象")
public class QualityProductMeasureVO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "统计月份")
    private LocalDate statisticMonth;

    @ApiModelProperty(value = "统计时间")
    private LocalDate statisticDate;

    @ApiModelProperty(value = "sonar分析时间")
    private LocalDate sonarDate;

    @ApiModelProperty(value = "安全评级")
    private String securityRating;

    @ApiModelProperty(value = "安全问题数量")
    private String securityIssues;

    @ApiModelProperty(value = "可靠性评级")
    private String reliabilityRating;

    @ApiModelProperty(value = "可维护性问题数量")
    private String reliabilityIssues;

    @ApiModelProperty(value = "新代码可维护性评级")
    private String newMaintainabilityRating;

    @ApiModelProperty(value = "可维护性问题数量")
    private String maintainabilityIssues;

    @ApiModelProperty(value = "重复代码行密度")
    private String duplicatedLinesDensity;

    @ApiModelProperty(value = "代码覆盖率")
    private String coverage;

    @ApiModelProperty(value = "非注释代码行数")
    private String ncloc;

    @ApiModelProperty(value = "年代码增量")
    private String annualIncrement;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

}
