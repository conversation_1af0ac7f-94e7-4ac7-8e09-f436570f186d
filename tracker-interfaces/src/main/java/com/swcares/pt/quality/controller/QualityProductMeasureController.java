package com.swcares.pt.quality.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.pt.common.cons.PmtApiVer;
import com.swcares.pt.quality.QualityProductMeasureAppService;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * ClassName：com.swcares.pt.quality.controller.QualityProductMeasureController <br>
 * Description：产品质量度量控制器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/quality/product-measure")
@ApiVersion(PmtApiVer.V1_0)
@Api(tags = "产品质量度量管理")
public class QualityProductMeasureController extends BaseController {

    @Autowired
    private QualityProductMeasureAppService qualityProductMeasureAppService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询产品质量度量")
    public BaseResult<IPage<QualityProductMeasureVO>> page(@RequestBody QualityProductMeasurePagedDTO dto) {
        IPage<QualityProductMeasureVO> page = qualityProductMeasureAppService.page(dto);
        return ok(page);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询产品质量度量列表")
    public BaseResult<List<QualityProductMeasureVO>> list(@RequestBody QualityProductMeasureDTO dto) {
        List<QualityProductMeasureVO> list = qualityProductMeasureAppService.getByCondition(dto);
        return ok(list);
    }

    @GetMapping("/latest")
    @ApiOperation(value = "查询产品最新质量度量")
    public BaseResult<QualityProductMeasureVO> getLatest(String productCode) {
        QualityProductMeasureDTO dto = QualityProductMeasureDTO.builder()
                .productCode(productCode)
                .build();
        List<QualityProductMeasureVO> list = qualityProductMeasureAppService.getByCondition(dto);
        QualityProductMeasureVO latest = null;
        if (list != null && !list.isEmpty()) {
            latest = list.get(0); // 已按时间倒序排列，第一条即为最新
        }
        return ok(latest);
    }

}
