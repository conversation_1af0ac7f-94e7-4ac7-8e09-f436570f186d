package com.swcares.pt.quality.converter;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.swcares.pt.quality.domain.QualityProductMeasure;
import com.swcares.pt.quality.entity.QualityProductMeasureDO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;

/**
 * ClassName：com.swcares.pt.quality.converter.QualityProductMeasureDOConverter <br>
 * Description：产品质量度量数据对象转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface QualityProductMeasureDOConverter {

    QualityProductMeasureDOConverter INSTANCE = Mappers.getMapper(QualityProductMeasureDOConverter.class);

    /**
     * Description：领域对象转换为数据对象 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param qpm 领域对象
     * @return 数据对象 <br>
     */
    QualityProductMeasureDO toDO(QualityProductMeasure qpm);

    /**
     * Description：数据对象转换为领域对象 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param qpmDO 数据对象
     * @return 领域对象 <br>
     */
    QualityProductMeasure fromDO(QualityProductMeasureDO qpmDO);

    /**
     * Description：视图对象转换为领域对象 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param qpmVO 视图对象
     * @return 领域对象 <br>
     */
    QualityProductMeasure fromVO(QualityProductMeasureVO qpmVO);

    /**
     * Description：领域对象列表转换为数据对象列表 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param qpms 领域对象列表
     * @return 数据对象列表 <br>
     */
    List<QualityProductMeasureDO> toDOs(List<QualityProductMeasure> qpms);

    /**
     * Description：数据对象列表转换为领域对象列表 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param qpmDOs 数据对象列表
     * @return 领域对象列表 <br>
     */
    List<QualityProductMeasure> fromDOs(List<QualityProductMeasureDO> qpmDOs);

}
