<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.swcares.pt</groupId>
		<artifactId>project-tracker</artifactId>
		<version>1.1.0-SNAPSHOT</version>
	</parent>
	
	<artifactId>tracker-aggregation</artifactId>
	
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-autoconfigure</artifactId>
		</dependency>
		
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
		</dependency>
		
        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
			<groupId>com.alibaba</groupId>
    		<artifactId>easyexcel</artifactId>
    		<version>3.3.1</version>
    		<exclusions>
    			<exclusion>
    				<groupId>org.ehcache</groupId>
      				<artifactId>ehcache</artifactId>
    			</exclusion>
    		</exclusions>
		</dependency>
		
		<dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>common-base</artifactId>
        </dependency>
        
		<dependency>
            <groupId>com.swcares.pt</groupId>
            <artifactId>tracker-commons</artifactId>
        </dependency>
		
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
        
        <dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>
		
	</dependencies>
	
</project>