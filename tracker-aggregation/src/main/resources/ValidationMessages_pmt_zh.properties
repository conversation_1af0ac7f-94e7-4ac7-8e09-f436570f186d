javax.validation.constraints.NotNull.message = \u4e0d\u80fd\u4e3a\u7a7a

# \u4e1a\u52a1\u5c5e\u6027\u540d\u79f0\u914d\u7f6e\uff0c\u6b64\u914d\u7f6e\u5e94\u7528\u4e8e\u5b9e\u4f53\u4e2d\u6ce8\u89e3message\u4e0a\uff0c\u4f8b\u5982\uff1a@NotNull(message={uc.user.username})

# \u9879\u76ee\u4fe1\u606f
ProjectInfoDTO.projectCode = \u9879\u76ee\u7f16\u7801
ProjectInfoDTO.projectSn = \u9879\u76ee\u7f16\u53f7
ProjectInfoDTO.projectName = \u9879\u76ee\u540d\u79f0
ProjectInfoDTO.projectAlias = \u9879\u76ee\u7b80\u79f0
ProjectInfoDTO.projectNature = \u9879\u76ee\u6027\u8d28
ProjectInfoDTO.customerName = \u5ba2\u6237\u540d\u79f0
ProjectInfoDTO.managementUnit = \u7ecf\u8425\u5355\u5143
ProjectInfoDTO.deliveryTeam = \u4ea4\u4ed8\u7ec4
ProjectInfoDTO.priorityLevel = \u4f18\u5148\u7ea7
ProjectInfoDTO.jobNumber = \u5de5\u53f7
ProjectInfoDTO.projectManager = \u9879\u76ee\u7ecf\u7406
ProjectInfoDTO.projectStage = \u9879\u76ee\u9636\u6bb5
ProjectInfoDTO.projectState = \u9879\u76ee\u72b6\u6001
ProjectInfoDTO.approvalDate = \u7acb\u9879\u65f6\u95f4
ProjectInfoDTO.planStartDate = \u8ba1\u5212\u542f\u52a8\u65f6\u95f4
ProjectInfoDTO.planEndDate = \u8ba1\u5212\u9a8c\u6536\u65f6\u95f4
ProjectInfoDTO.planClosingDate = \u8ba1\u5212\u7ed3\u9879\u65f6\u95f4
ProjectInfoDTO.budgetProject = \u9884\u7b97\u5185\u9879\u76ee(0-\u5426,1-\u662f)
ProjectInfoDTO.pretaxIncome = \u7a0e\u524d\u6536\u5165
ProjectInfoDTO.revenue = \u7a0e\u540e\u6536\u5165
ProjectInfoDTO.remark = \u5907\u6ce8

# \u9879\u76ee\u91cc\u7a0b\u7891
ProjectMilestoneDTO.milestoneCode = \u91cc\u7a0b\u7891\u7f16\u53f7
ProjectMilestoneDTO.milestoneName = \u91cc\u7a0b\u7891\u540d\u79f0
ProjectMilestoneDTO.projectCode = \u9879\u76ee\u7f16\u53f7
ProjectMilestoneDTO.projectYear = \u9879\u76ee\u5e74\u5ea6
ProjectMilestoneDTO.managementUnit = \u7ecf\u8425\u5355\u5143
ProjectMilestoneDTO.jobNumber = \u9879\u76ee\u7ecf\u7406\u5de5\u53f7
ProjectMilestoneDTO.deliveryTeam = \u4ea4\u4ed8\u7ec4
ProjectMilestoneDTO.startDate = \u5f00\u59cb\u65e5\u671f
ProjectMilestoneDTO.endDate = \u7ed3\u675f\u65e5\u671f
ProjectMilestoneDTO.workDay = \u5de5\u4f5c\u65e5\uff08\u5929\uff09
ProjectMilestoneDTO.planWorkLoad = \u8ba1\u5212\u5de5\u91cf\uff08\u4eba\u5929\uff09
ProjectMilestoneDTO.remark = \u5907\u6ce8

# \u9879\u76ee\u5468\u62a5
ProjectWeeklyDTO.projectCode = \u9879\u76ee\u7f16\u53f7
ProjectWeeklyDTO.monthPlanCode = \u6708\u8ba1\u5212\u7f16\u53f7
ProjectWeeklyDTO.weekPlanCode = \u5468\u8ba1\u5212\u7f16\u53f7
ProjectWeeklyDTO.year = \u5e74\u4efd
ProjectWeeklyDTO.weekMonth = \u6240\u5c5e\u6708
ProjectWeeklyDTO.yearWeekNum = \u5e74\u5ea6\u7b2c\u51e0\u5468
ProjectWeeklyDTO.workDays = \u5de5\u4f5c\u5929\u6570
ProjectWeeklyDTO.managementUnit = \u7ecf\u8425\u5355\u5143
ProjectWeeklyDTO.deliveryTeam = \u4ea4\u4ed8\u7ec4
ProjectWeeklyDTO.jobNumber = \u9879\u76ee\u7ecf\u7406\uff08\u5de5\u53f7\uff09
ProjectWeeklyDTO.projectManager = \u9879\u76ee\u7ecf\u7406
ProjectWeeklyDTO.currentWeekEv = \u672c\u5468EV
ProjectWeeklyDTO.weekTotalEv = \u7d2f\u8ba1EV
ProjectWeeklyDTO.weekTotalPv = \u7d2f\u8ba1PV
ProjectWeeklyDTO.lastWeekTotalEv = \u4e0a\u5468\u7d2f\u8ba1EV
ProjectWeeklyDTO.paceOffset = \u8fdb\u5ea6\u504f\u5dee
ProjectWeeklyDTO.fillInBy = \u8ddf\u8e2a\u4eba
ProjectWeeklyDTO.fillInTime = \u8ddf\u8e2a\u65f6\u95f4
ProjectWeeklyDTO.paceRisk = \u8fdb\u5ea6\u98ce\u9669
ProjectWeeklyDTO.startDate = \u5f00\u59cb\u65e5\u671f
ProjectWeeklyDTO.endDate = \u7ed3\u675f\u65e5\u671f
ProjectWeeklyDTO.weeklyState = \u72b6\u6001(1-\u5f85\u586b\u5199,2-\u5df2\u586b\u5199,3-\u5df2\u5173\u95ed)
ProjectWeeklyDTO.description = \u8fdb\u5c55\u63cf\u8ff0
ProjectWeeklyDTO.remark = \u5907\u6ce8

# \u9879\u76ee\u98ce\u9669
ProjectRiskTrackDTO.projectCode = \u9879\u76ee\u7f16\u53f7
ProjectRiskTrackDTO.riskName = \u98ce\u9669\u540d\u79f0
ProjectRiskTrackDTO.riskType = \u98ce\u9669\u7c7b\u578b
ProjectRiskTrackDTO.riskState = \u72b6\u6001
ProjectRiskTrackDTO.description = \u8fdb\u5c55\u63cf\u8ff0
ProjectRiskTrackDTO.closeDate = \u5173\u95ed\u65e5\u671f
