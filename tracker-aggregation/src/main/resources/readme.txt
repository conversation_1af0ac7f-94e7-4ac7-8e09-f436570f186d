
领域模型层 domain
	包括实体、值对象、领域工厂、领域服务（处理本聚合内跨实体操作）、资源库接口、自定义异常等
domain 领域层：
	负责表达业务概念和业务逻辑，领域层是系统的核心。包含：模型，值对象，域服务，事件。域服务，聚合根，值对象，领域参数，仓储定义;
	最纯粹的抽象接口定义层。

领域：业务范围，范围就是边界。

子领域：领域可大可小，将一个领域进行拆解形成子领域，子领域还可以进行拆解。当一个领域太大的时候需要进行细化拆解。

模型（战术）：基于某个业务领域识别出这个业务领域的聚合，聚合根，界限上下文，实体，值对象。

核实域：
	决定产品和核心竞争力的子域为核心域，它是业务成功的主要因素和核心竞争力。直接对业务产生价值。
通用域：
	没有太多个性化诉求，同时被多个子域使用的通用功能子域为通用域。例如：权限、登录等等。间接对业务产生价值。
支撑域：
	支撑其他领域业务，具有企业特性，但不具有通用性。间接对业务产生价值。

一个业务一定有他最重要的部分，在日常做业务判断和需求优先级判断的时候可以基于这个划分来做决策。例如：一个交易相关的需求和一个配置相关的需求排优先级，
很明显交易是核心域，规则是支持域。同样我们认为是支撑域或者通用域的在其他公司可能是核心域，例如权限对于我们来说是通用域，但是对于专业做权限系统的公司，这个是核心域。

界限上下文：
	业务的边界的划分，这个边界可以是一个领域或者多个领域的集合。复杂业务需要多个域编排完成一个复杂业务流程。限界上下文可以作为微服务划分的方法。其本质还是高内聚低耦合，
	只是限界上下文只是站在更高的层面来进行划分。如何进行划分，我的方法是一个界限上下文必须支持一个完整的业务流程，保证这个业务流程所涉及的领域都在一个限界上下文中。
实体：
	实体有唯一的标识，有生命周期且具有延续性。例如一个交易订单，从创建订单我们会给他一个订单编号并且是唯一的这就是实体唯一标识。同时订单实体会从创建，支付，
	发货等过程最终走到终态这就是实体的生命周期。订单实体在这个过程中属性发生了变化，但订单还是那个订单，不会因为属性的变化而变化，这就是实体的延续性。
	
	实体的业务形态：
		实体能够反映业务的真实形态，实体是从用例提取出来的。领域模型中的实体是多个属性、操作或行为的载体。
	实体的代码形态：
		我们要保证实体代码形态与业务形态的一致性。那么实体的代码应该也有属性和行为，也就是我们说的充血模型，但实际情况下我们使用的是贫血模型。贫血模型缺点是业务逻辑分散，
		更像数据库模型，充血模型能够反映业务，但过重依赖数据库操作，而且复杂场景下需要编排领域服务，会导致事务过长，影响性能。所以我们使用充血模型，但行为里面只涉及业务逻辑的内存操作。
	实体的运行形态：
		实体有唯一ID，当我们在流程中对实体属性进行修改，但ID不会变，实体还是那个实体。
	实体的数据库形态：
		实体在映射数据库模型时，一般是一对一，也有一对多的情况。
值对象：
	通过对象属性值来识别的对象，它将多个相关属性组合为一个概念整体。在 DDD 中用来描述领域的特定方面，并且是一个没有标识符的对象，叫作值对象。值对象没有唯一标识，没有生命周期，不可修改，
	当值对象发生改变时只能替换（例如String的实现）
	
	值对象的业务形态：
		值对象是描述实体的特征，大多数情况一个实体有很多属性，一般都是平铺，这些数据进行分类和聚合后能够表达一个业务含义，方便沟通而不关注细节。

	值对象的代码形态：
		实体的单一属性是值对象，例如：字符串，整型，枚举。多个属性的集合也是值对象，这个时候我们把这个集合设计为一个CLASS，但没有ID。例如商品实体下的航段就是一个值对象。
		航段是描述商品的特征，航段不需要ID，可以直接整体替换。商品为什么是一个实体，而不是描述订单特征，因为需要表达谁买了什么商品，所以我们需要知道哪一个商品，因此需要ID来标识唯一性。
	值对象的运行形态：
		值对象创建后就不允许修改了，只能用另外一个值对象来整体替换。当我们修改地址时，从页面传入一个新的地址对象替换调用person对象的地址即可。如果我们把address设计成实体，
		必然存在ID，那么我们需要从页面传入的地址对象的ID与person里面的地址对像的ID进行比较，如果相同就更新，如果不同先删除数据库在新增数据。

	值对象的数据库形态：有两种方式嵌入式和序列化大对象。
	
	值对象的优势和局限：
		1. 简化数据库设计，提升数据库操作的性能（多表新增和修改，关联表查询）。
		2. 虽然简化数据库设计，但是领域模型还是可以表达业务。
		3. 序列化的方式会使搜索实现困难（通过搜索引擎可以解决）。	
聚合和聚合根:
	多个实体和值对象组成的我们叫聚合，聚合的内部一定的高内聚。这个聚合里面一定有一个实体是聚合根。
	聚合与领域的关系：聚合也是范围的划分，领域也是范围的划分。领域与聚合可以是一对一，也可以是一对多的关系
	聚合根的作用是保证内部的实体的一致性，对外只需要对聚合根进行操作。	
	
限界上下文，域，聚合，实体，值对象的关系:
	领域包含限界上下文，限界上下文包含子域，子域包含聚合，聚合包含实体和值对象.	
	
	
	
事件风暴:
参与者
	除了领域专家，事件风暴的其他参与者可以是DDD专家、架构师、产品经理、项目经理、开发人员和测试人员等项目团队成员

事件风暴准备的材料
	一面墙和一支笔。

事件风暴的关注点
	在领域建模的过程中，我们需要重点关注这类业务的语言和行为。比如某些业务动作或行为（事件）是否会触发下一个业务动作，这个动作（事件）的输入和输出是什么？
	是谁（实体）发出的什么动作（命令），触发了这个动作（事件）…我们可以从这些暗藏的词汇中，分析出领域模型中的事件、命令和实体等领域对象。
	实体执行命令产生事件。

业务场景的分析
	通过业务场景和用例找出实体，命令，事件。

领域建模
	领域建模时，我们会根据场景分析过程中产生的领域对象，比如命令、事件等之间关系，找出产生命令的实体，分析实体之间的依赖关系组成聚合，为聚合划定限界上下文，
	建立领域模型以及模型之间的依赖。领域模型利用限界上下文向上可以指导微服务设计，通过聚合向下可以指导聚合根、实体和值对象的设计。	
	
	
	
	
	
	
	
	
	
		
	
	
