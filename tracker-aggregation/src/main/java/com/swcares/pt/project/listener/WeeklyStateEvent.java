/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyCloseEvent.java <br>
 * Package：com.swcares.pt.project.event <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月26日 上午10:15:45 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.listener;

import com.swcares.pt.enums.WeeklyStateEnum;
import com.swcares.pt.project.domain.ProjectWeekly;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**   
 * ClassName：com.swcares.pt.project.event.WeeklyCloseEvent <br>
 * Description：周报关闭事件 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月26日 上午10:15:45 <br>
 * @version v1.0 <br>  
 */
@Data
@Accessors(chain = true)
@Builder
public class WeeklyStateEvent {

	private WeeklyStateEnum state;
	
	private ProjectWeekly weekly;
	
	public boolean isClosed() {
		return state != null ? WeeklyStateEnum.isClosed(state.getCode()) : Boolean.FALSE;
	}
	
}
