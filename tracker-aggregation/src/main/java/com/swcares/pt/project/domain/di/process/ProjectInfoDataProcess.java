/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectInfoDataProcessing.java <br>
 * Package：com.swcares.pt.di.process <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月27日 下午1:41:30 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.di.process;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.di.DataImportBase;
import com.swcares.pt.di.DataImportProcess;
import com.swcares.pt.di.ImportContext;
import com.swcares.pt.project.domain.ProjectInfo;
import com.swcares.pt.project.dto.ProjectInfoDTO;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.repository.ProjectInfoRepository;
import com.swcares.pt.project.vo.ProjectInfoVO;

/**
 * ClassName：com.swcares.pt.di.process.ProjectInfoDataProcessing <br>
 * Description：项目总览数据导入处理实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * 
 * <AUTHOR> <br>
 * date 2024年8月27日 下午1:41:30 <br>
 * @version v1.0 <br>
 */
@Component
public class ProjectInfoDataProcess extends DataImportBase<ProjectInfo, ProjectInfoVO>
		implements DataImportProcess<ProjectInfo, ProjectInfoVO> {

	@Autowired
	private ProjectInfoRepository repository;

	@Override
	public List<ProjectInfo> process(List<ProjectInfo> sources) {
		List<ProjectInfoVO> originals = repository.getByCondition(ProjectInfoDTO.builder().build());

		ImportContext<ProjectInfo, ProjectInfoVO> ctx = ImportContext.<ProjectInfo, ProjectInfoVO>builder()
				.importDatas(sources)
				.originals(originals)
				.build();
		if (ctx.isOriginal()) {
			findOriginalData(ctx);
		}
		return ctx.getImportDatas();
	}

	@Override
	public void findOriginalData(ImportContext<ProjectInfo, ProjectInfoVO> ctx) {
		List<ProjectInfoVO> his = ctx.getOriginals();
		Map<String, ProjectInfoVO> originals = his.stream().collect(Collectors.toMap(p -> p.getProjectCode(), v -> v));
		ctx.getImportDatas().forEach(p -> {
			ProjectInfoVO org = originals.get(p.getProjectCode());
			if (org != null) {
				p.setId(org.getId());
			}
		});
	}

	@Override
	protected BaseRepository<ProjectInfo, ProjectInfoVO> getBaseRepository() {
		return this.repository;
	}

	@Override
	protected QueryParam buildParams(ProjectInfo s) {
		return null;
	}

}
