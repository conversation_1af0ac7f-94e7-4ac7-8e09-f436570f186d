package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.PagedDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectMonthCostPagedDTO <br>
 * Description：项目月成本 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectMonthCostPagedDTO", description="项目月成本PagedDTO")
public class ProjectMonthCostPagedDTO extends PagedDTO implements Serializable{

	private static final long serialVersionUID = 7139321274530384708L;

	@ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目年")
    private String projectYear;
    
    @ApiModelProperty(value = "项目简称")
    private String projectAlias;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "统计月份")
    private String statMonth;

    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;

}
