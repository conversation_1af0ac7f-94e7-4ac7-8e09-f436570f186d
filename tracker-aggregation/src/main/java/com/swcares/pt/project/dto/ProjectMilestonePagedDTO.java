package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import com.swcares.pt.core.DataScopeHelper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectMilestonePagedDTO <br>
 * Description：项目里程碑 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-12 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectMilestonePagedDTO", description="项目里程碑")
public class ProjectMilestonePagedDTO extends PagedDTO implements DataScopeHelper, Serializable{

	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;

    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "项目年")
    private Integer projectYear;

    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "工号")
    private String jobNumber;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "工作日（天）")
    private Integer workDay;

    @ApiModelProperty(value = "计划工量（人天）")
    private BigDecimal planWorkLoad;

    @ApiModelProperty(value = "备注")
    private String remark;

}
