package com.swcares.pt.project.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.pt.core.DataScopeHelper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectWeeklyDTO <br>
 * Description：项目周报DTO <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectWeeklyDTO", description="项目周报DTO")
public class ProjectWeeklyDTO extends BaseEntity implements DataScopeHelper{

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "生成周报日期")
    private String weeklyDate;
    
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;

    @ApiModelProperty(value = "周计划编号")
    private String weekPlanCode;

    @ApiModelProperty(value = "所属月")
    private String weekMonth;
    
    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "年度第几周")
    private Integer yearWeekNum;
    
    @ApiModelProperty(value = "工作天数")
    private Integer workDays;

    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;

    @ApiModelProperty(value = "项目经理（工号）")
    private String jobNumber;
    
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @NotNull(message = "本周EV不能为空")
    @ApiModelProperty(value = "本周EV")
    private BigDecimal currentWeekEv;

    @NotNull(message = "累计EV不能为空")
    @ApiModelProperty(value = "累计EV")
    private BigDecimal weekTotalEv;

    @NotNull(message = "累计PV不能为空")
    @ApiModelProperty(value = "累计PV")
    private BigDecimal weekTotalPv;

    @ApiModelProperty(value = "上周累计EV")
    private BigDecimal lastWeekTotalEv;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;

    @ApiModelProperty(value = "填报人")
    private String fillInBy;

    @ApiModelProperty(value = "填报时间")
    private LocalDateTime fillInTime;

    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;
    
    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "状态(0-待填写, 1-已填写, 2-已关闭)")
    private Integer weeklyState;

    @ApiModelProperty(value = "进展描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "周报明细")
    private List<WeeklyItemsDTO> items;

}
