package com.swcares.pt.project.repository;

import java.util.List;
import java.util.Map;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.dto.WeeklyItemsDTO;
import com.swcares.pt.project.vo.WeeklyItemsVO;

/**   
 * ClassName：com.swcares.pt.project.repository.WeeklyItemsRepository <br>
 * Description：项目周报明细项仓储接口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:04:36 <br>
 * @version v1.0 <br>  
 */
public interface WeeklyItemsRepository extends BaseRepository<WeeklyItems, WeeklyItemsVO> {

	/**
	 * Description：根据查询条件加载项目周报明细项数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<WeeklyItemsVO> getByCondition(WeeklyItemsDTO params);
	
	/**
	 * Description：根据周报ID加载明细项 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 下午3:34:56 <br>
	 * @param weeklyIds
	 * @return <br>
	 */
	Map<Long, List<WeeklyItemsVO>> getByWeeklyIds(List<Long> weeklyIds);
	
}
