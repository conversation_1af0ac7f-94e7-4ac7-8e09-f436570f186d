/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectWeeklyWrapper.java <br>
 * Package：com.swcares.pt.project.domain.service <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月18日 上午8:55:51 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.context.WeeklyContext;

/**   
 * ClassName：com.swcares.pt.project.domain.service.ProjectWeeklyWrapper <br>
 * Description：项目周报业务辅助类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月18日 上午8:55:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectWeeklyWrapper {

	@Autowired
	private ProjectWeeklyItemWrapper itemWrapper;
	
	/**
	 * Description：根据上下文内容构建生成下周周报 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 上午11:46:11 <br>
	 * @param ctx
	 * @return <br>
	 */
	public List<ProjectWeekly> apply(WeeklyContext ctx){
		ctx.process(itemWrapper);
		return ctx.getCurrentWeeklys();
	} 
	
}
