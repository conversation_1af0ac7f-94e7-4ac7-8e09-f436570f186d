package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.common.model.LoginUser;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.enums.WeeklyStateEnum;
import com.swcares.pt.project.domain.assembler.WeeklyAssembler;
import com.swcares.pt.project.domain.context.WeeklyContext;
import com.swcares.pt.project.repository.ProjectWeeklyRepository;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectWeekly <br>
 * Description：项目周报Domain <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ProjectWeekly extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编号 */
    private String projectCode;

    /** 月计划编号 */
    private String monthPlanCode;

    /** 周计划编号 */
    private String weekPlanCode;

    /** 所属月 */
    private String weekMonth;
    
    /** 年份 */
    private Integer year;

    /** 年度第几周 */
    private Integer yearWeekNum;
    
    /** 工作天数 */
    private Integer workDays;

    /** 经营单元 */
    private Long managementUnit;

    /** 交付组 */
    private Long deliveryTeam;

    /** 项目经理（工号） */
    private String jobNumber;
    
    /** 项目经理 */
    private String projectManager;

    /** 本周EV */
    private BigDecimal currentWeekEv;

    /** 累计EV */
    private BigDecimal weekTotalEv;

    /** 累计PV */
    private BigDecimal weekTotalPv;

    /** 上周累计EV */
    private BigDecimal lastWeekTotalEv;

    /** 进度偏差 */
    private BigDecimal paceOffset;

    /** 填报人 */
    private String fillInBy;

    /** 填报时间 */
    private LocalDateTime fillInTime;

    /** 进度风险 */
    private Integer paceRisk;

    /** 开始日期 */
    private LocalDate startDate;

    /** 结束日期 */
    private LocalDate endDate;

    /** 状态(1-待填写,2-已填写,3-已关闭) */
    private Integer weeklyState;

    /** 进展描述 */
    private String description;

    /** 备注 */
    private String remark;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;

    
    private ProjectWeeklyRepository repository;
    
    private List<WeeklyItems> items;
    /** 风险偏差阈值 */
    private Threshold threshold;
    
    private ProjectWeekly last;
    
    private ProjectWeekly nextWeekly;
    
    private Boolean isClose = Boolean.FALSE; 
    
	public ProjectWeekly(ProjectWeeklyRepository repository) {
		this.repository = repository;
	}

	public static ProjectWeekly build(ProjectWeeklyRepository repository) {
		return new ProjectWeekly(repository);
	}

	public boolean save() {
		valifyWeekly();
		calculatePaceOffset();
		return this.repository.save(this);
	}
	
	public void addWeeklyItems(WeeklyItems item) {
		if(this.items == null) {
			this.items = ListUtils.newArrayList();
		}
		this.items.add(item);
	}
	
	public void calculatePaceOffset() {
		if(this.weekTotalEv == null || this.weekTotalPv == null) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "计算【"+projectCode+"】进度偏差异常，累计EV或PV为空!");
		}
		valifyThreshold();
//		this.paceOffset = this.weekTotalEv.divide(this.weekTotalPv.subtract(new BigDecimal(1)), 2, RoundingMode.HALF_UP);
		int countor = calculateMilestonePaceOffset();
		if(this.paceOffset == null) {
			this.paceOffset = new BigDecimal(0);
		}
		this.paceRisk = (this.paceOffset.compareTo(this.threshold.getOffsetThreshold()) <= 0 || countor > this.threshold.getLagQuantity()) ? 1 : 0;
	}
	
	public ProjectWeekly fillIn() {
		LoginUser user = this.getUser();
		if(user != null) {
			this.fillInBy = user.getUsername();
			this.fillInTime = LdtUtils.now();
		}
		return this;
	}
	
	public ProjectWeekly write() {
		this.weeklyState = WeeklyStateEnum.wrote.getCode();
		return this;
	}
	
	private int calculateMilestonePaceOffset() {
		if(ListUtils.isEmpty(this.items)) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "计算里程碑进度偏差异常，里程碑为空!");
		}
		valifyThreshold();
		int countor = 0;
		for (WeeklyItems w : items) {
			if(w.calculatePaceOffset(this.threshold.getOffsetThreshold())) {
				countor ++;
			}
		} 
		return countor;
	}
	
	private void valifyWeekly() {
		ProjectWeeklyVO original = repository.getById(getId());
		if(original != null && WeeklyStateEnum.isClosed(original.getWeeklyState())) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "周报已关闭，不能再编辑！");
		}
		this.last = WeeklyStateEnum.isNotWrite(original.getWeeklyState()) ? buildLastWeekly() : null;
	}
	
	private ProjectWeekly buildLastWeekly() {
		WeeklyContext ctx = WeeklyContext.builder().year(year).weekSerial(this.yearWeekNum).build().initWeekDate(endDate.toString());
		WeekInfo lw = ctx.getWeek().getLastWeek();
		ProjectWeeklyVO lastVO = repository.getWeekly(this.projectCode, lw.getYear(), lw.getWeekSerial());
		ProjectWeekly lastWeekly = null;
		if(lastVO != null && WeeklyStateEnum.isWroted(lastVO.getWeeklyState())) {
			lastWeekly = WeeklyAssembler.INSTANCE.toDomain(lastVO, getUser());
			lastWeekly.setWeeklyState(WeeklyStateEnum.closed.getCode());
			lastWeekly.close();
		}
		return lastWeekly;
	}
	
	private ProjectWeekly close() {
		this.isClose = Boolean.TRUE;
		return this;
	}
	
	private void valifyThreshold() {
		if(this.threshold == null) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "阈值不能为空!");
		}
	}

}
