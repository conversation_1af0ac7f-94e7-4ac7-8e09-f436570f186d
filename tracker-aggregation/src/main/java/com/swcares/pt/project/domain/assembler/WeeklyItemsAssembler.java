/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyAssembler.java <br>
 * Package：com.swcares.pt.project.assembler <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:41:51 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.assembler;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.LoginUser;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.domain.factory.WeeklyItemsFactory;
import com.swcares.pt.project.dto.WeeklyItemsDTO;
import com.swcares.pt.project.vo.WeeklyItemsVO;

/**   
 * ClassName：com.swcares.pt.project.assembler.WeeklyAssembler <br>
 * Description：WeeklyItems域对象转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:41:51 <br>
 * @version v1.0 <br>  
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
		nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WeeklyItemsAssembler {

	WeeklyItemsAssembler INSTANCE = Mappers.getMapper(WeeklyItemsAssembler.class);
	
	default WeeklyItems toDomain(WeeklyItemsDTO dto, LoginUser user) {
		WeeklyItems state = WeeklyItemsFactory.create();
		INSTANCE.update(dto, user, state);
		return state;
	}
	
	default WeeklyItems toDomain(WeeklyItemsVO vo, LoginUser user) {
		WeeklyItems state = WeeklyItemsFactory.create();
		INSTANCE.update(vo, user, state);
		return state;
	}
	
	default List<WeeklyItems> toDomains(List<WeeklyItemsDTO> states, LoginUser user){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<WeeklyItems> datas = ListUtils.newArrayList();
		states.forEach(s -> {
			datas.add(WeeklyItemsAssembler.INSTANCE.toDomain(s, user));
		});
		return datas;
	}
	
	default List<WeeklyItems> toDomains(LoginUser user, List<WeeklyItemsVO> states){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<WeeklyItems> datas = ListUtils.newArrayList();
		states.forEach(v -> {
			datas.add(WeeklyItemsAssembler.INSTANCE.toDomain(v, user));
		});
		return datas;
	}
	
	@Mapping(target = "endDate", ignore = true)
	@Mapping(target = "startDate", ignore = true)
	@Mapping(target = "milestoneName", ignore = true)
	@Mapping(target = "planWorkLoad", ignore = true)
	WeeklyItemsVO toVO(WeeklyItems pi);
	
	default List<WeeklyItemsVO> toVOs(List<WeeklyItems> items){
		if(ListUtils.isEmpty(items)) {
			return ListUtils.newArrayList();
		}
		List<WeeklyItemsVO> datas = ListUtils.newArrayList();
		items.forEach(v -> {
			datas.add(WeeklyItemsAssembler.INSTANCE.toVO(v));
		});
		return datas;
	}
	
	/**
	WeeklyItemsDTO toDTO(WeeklyItemsBO pi);
	
	default List<WeeklyItemsDTO> toDTOs(List<WeeklyItemsBO> pis){
		if(ListUtils.isEmpty(pis)) {
			return ListUtils.newArrayList();
		}
		return pis.stream().map(WeeklyAssembler.INSTANCE::toDTO).collect(Collectors.toList());
	}*/
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "deleted", ignore = true)
	@Mapping(target = "paceRisk", ignore = true)
	@Mapping(source = "dto.id", target = "id")
	void update(WeeklyItemsDTO dto, LoginUser user, @MappingTarget WeeklyItems state);
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "deleted", ignore = true)
	@Mapping(target = "paceRisk", ignore = true)
	@Mapping(source = "dto.id", target = "id")
	void update(WeeklyItemsVO dto, LoginUser user, @MappingTarget WeeklyItems state);
	
}
