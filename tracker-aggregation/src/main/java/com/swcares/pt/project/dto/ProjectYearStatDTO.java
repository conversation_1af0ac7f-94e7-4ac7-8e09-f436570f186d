package com.swcares.pt.project.dto;

import java.math.BigDecimal;

import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectYearStatDTO <br>
 * Description：项目年统计 <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectYearStatDTO", description="项目年统计DTO")
public class ProjectYearStatDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目年度")
    private Integer projectYear;
    
    @ApiModelProperty(value = "年度计划工量")
    private BigDecimal planWorkLoad;

    @ApiModelProperty(value = "年度计划成本")
    private BigDecimal yearPlanCost;

    @ApiModelProperty(value = "年度人工单价(元/人年)")
    private BigDecimal yearCostPrice;

    @ApiModelProperty(value = "备注")
    private String remark;

}
