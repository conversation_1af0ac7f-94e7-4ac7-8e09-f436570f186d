/**  
 * All rights Reserved, Designed By <br>
 * Title：ManagementUnitStatVO.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 下午3:25:22 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.vo.ManagementUnitStatVO <br>
 * Description：经营单元统计分析 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 下午3:25:22 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="ManagementUnitStatVO", description="经营单元统计分析VO")
public class ManagementUnitStatVO {

	private Long managementUnit;
	
	private String managementUnitName;
	
	List<ManagementUnitMonthStatVO> stats;
	
}
