package com.swcares.pt.project.repository;

import java.time.LocalDate;
import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectWeekPlanRepository <br>
 * Description：项目周计划仓储接口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:04:36 <br>
 * @version v1.0 <br>  
 */
public interface ProjectWeekPlanRepository extends BaseRepository<ProjectWeekPlan, ProjectWeekPlanVO> {

	/**
	 * Description：根据查询条件加载项目周计划数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectWeekPlanVO> getByCondition(ProjectWeekPlanDTO params);
	
	/**
	 * Description：根据年份、小于等于第几周和项目编号等查询条件获取项目周计划 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 上午10:57:43 <br>
	 * @param year
	 * @param weekSerial
	 * @param projectCodes
	 * @return <br>
	 */
	List<ProjectWeekPlanVO> getWeekPlans(Integer year, Integer weekSerial, List<String> projectCodes, LocalDate startDate);
	
}
