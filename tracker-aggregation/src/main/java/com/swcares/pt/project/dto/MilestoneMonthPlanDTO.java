package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.pt.core.DataScopeHelper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectMonthPlanDTO <br>
 * Description：里程碑月计划 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@ApiModel(value="MilestoneMonthPlanDTO", description="里程碑月计划")
public class MilestoneMonthPlanDTO extends BaseEntity implements BaseDTO, DataScopeHelper, Serializable {

	private static final long serialVersionUID = -3824491009185118785L;

	@ApiModelProperty(value = "主键ID")
    private Long id;
	
	@ApiModelProperty(value = "计划编号")
	private String planCode;

	@NotNull(message = "项目编号不能为空")
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
	
	@ApiModelProperty(value = "里程碑编号")
	private String milestoneCode;

    @ApiModelProperty(value = "计划月份")
    private String planMonth; 
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber; 
    
    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "月工作日(天)")
    private Integer workDay;
    
    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;
    
    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "当月累计PV(人天)")
    private BigDecimal planWorkLoad;
    
    @ApiModelProperty(value = "当月PV(人天)")
    private BigDecimal planMonthWorkLoad;


}
