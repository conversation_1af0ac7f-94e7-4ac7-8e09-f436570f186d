/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectInfoFactory.java <br>
 * Package：com.swcares.pt.project.domain.factory <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:58:50 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.repository.ProjectMonthPlanRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.ProjectMonthCostFactory <br>
 * Description：项目信息对象工厂类 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:58:50 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectMonthPlanFactory {

	private static ProjectMonthPlanRepository repositroy;
	
	@Autowired
	public ProjectMonthPlanFactory(ProjectMonthPlanRepository repositroy) {
		ProjectMonthPlanFactory.repositroy = repositroy;
	}
	
	public static ProjectMonthPlan create() {
		return ProjectMonthPlan.build(repositroy);
	}
	
}
