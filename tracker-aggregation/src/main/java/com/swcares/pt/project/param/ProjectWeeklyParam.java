/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectWeeklyParam.java <br>
 * Package：com.swcares.pt.project.param <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月19日 下午1:50:42 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.param;

import com.swcares.baseframe.common.base.entity.SearchPagedDTO;
import com.swcares.pt.common.model.LoginUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.param.ProjectWeeklyParam <br>
 * Description：TODO(这里用一句话描述这个类的作用) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月19日 下午1:50:42 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ProjectWeeklyParam", description="项目周报Param")
public class ProjectWeeklyParam extends SearchPagedDTO{

	@ApiModelProperty(value = "周报主键ID")
	private Long id;
	
	@ApiModelProperty(value = "生成周报日期")
    private String weeklyDate;
	
	@ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;

    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;

    @ApiModelProperty(value = "周计划编号")
    private String weekPlanCode;

    @ApiModelProperty(value = "所属月")
    private String weekMonth;
    
    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "年度第几周")
    private Integer yearWeekNum;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;

    @ApiModelProperty(value = "项目经理（工号）")
    private String jobNumber;
	
    @ApiModelProperty(value = "跟踪人")
    private String trackedBy;
    
    @ApiModelProperty(value = "状态(1-待填写,2-已填写,3-已关闭)")
    private Integer weeklyState;
    
    private LoginUser user;

}
