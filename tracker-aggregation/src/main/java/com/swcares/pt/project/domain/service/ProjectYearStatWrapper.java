/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectYearStatWrapper.java <br>
 * Package：com.swcares.pt.project.domain.service <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月20日 下午1:09:15 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.ProjectYearStat;
import com.swcares.pt.project.domain.factory.ProjectYearStatFactory;
import com.swcares.pt.project.dto.ProjectYearStatDTO;
import com.swcares.pt.project.repository.ProjectYearStatRepository;
import com.swcares.pt.project.vo.ProjectYearStatVO;

/**   
 * ClassName：com.swcares.pt.project.domain.service.ProjectYearStatWrapper <br>
 * Description：项目年统计业务辅助处理类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月20日 下午1:09:15 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectYearStatWrapper {

	@Autowired
	private ProjectYearStatRepository repository;
	
	/**
	 * Description：根据里程碑对象构建出项目年统计对象 <br>
	 * author：罗江林 <br>
	 * date：2025年3月20日 下午1:19:57 <br>
	 * @param datas
	 * @return <br>
	 */
	public List<ProjectYearStat> applyProjectYearStat(List<ProjectMilestone> datas){
		Map<String, List<ProjectMilestone>> milestones = datas.stream().collect(Collectors.groupingBy(m -> m.getProjectCode()));
		List<ProjectYearStat> stats = ListUtils.newArrayList();
		for (String key : milestones.keySet()) {
			List<ProjectMilestone> pms = milestones.get(key);
			Map<Integer, List<ProjectMilestone>> collect = pms.stream().collect(Collectors.groupingBy(p -> p.getProjectYear()));
			for (Integer year : collect.keySet()) {
				ProjectYearStat stat = ProjectYearStatFactory.create();
				stat.setMilestones(collect.get(year));
				stat.process(key);
				stats.add(stat);
			}
		}
		Map<Integer, List<ProjectYearStat>> yss = stats.stream().collect(Collectors.groupingBy(p -> p.getProjectYear()));
		return findOriginalData(yss);
	}
	
	private List<ProjectYearStat> findOriginalData(Map<Integer, List<ProjectYearStat>> yss){
		List<ProjectYearStat> rs = ListUtils.newArrayList();
		for (Integer year : yss.keySet()) {
			List<ProjectYearStat> datas = yss.get(year);
			findOriginalData(datas, year);
			rs.addAll(datas);
		}
		return rs;
	}
	
	private void findOriginalData(List<ProjectYearStat> stats, Integer year){
		List<ProjectYearStatVO> datas = repository.getByCondition(ProjectYearStatDTO.builder().projectYear(year).build());
		if(ListUtils.isNotEmpty(datas)) {
			Map<String, ProjectYearStatVO> originals = datas.stream().collect(Collectors.toMap(s -> s.getProjectCode(), v -> v));
			stats.forEach(s -> {
				ProjectYearStatVO ys = originals.get(s.getProjectCode());
				if(ys != null) {
					s.setId(ys.getId());
				}
			});
		}
	};
	
}
