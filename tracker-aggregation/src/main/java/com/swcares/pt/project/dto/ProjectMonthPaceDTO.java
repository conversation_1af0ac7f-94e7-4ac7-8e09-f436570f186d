package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectMonthPaceDTO <br>
 * Description：项目月进度 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@ApiModel(value="ProjectMonthPaceDTO", description="项目月进度DTO")
public class ProjectMonthPaceDTO extends BaseEntity implements BaseDTO, Serializable {

	private static final long serialVersionUID = -3128061116143257256L;

	@ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;
    
    @ApiModelProperty(value = "所属年度")
    private Integer projectYear;
	
	@ApiModelProperty(value = "经营单元")
    private Long managementUnit;
	
	@ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

    @ApiModelProperty(value = "统计截止月份")
    private String statMonth;

    @ApiModelProperty(value = "统计截止日期")
    private LocalDate statDate;

    @ApiModelProperty(value = "当月累计PV(人天)")
    private BigDecimal planWorkLoad;
    
    @ApiModelProperty(value = "当月累计EV(人天)")
    private BigDecimal workLoadTotal;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;

    @ApiModelProperty(value = "里程碑滞后数量")
    private Integer milestoneOverdue;

    @ApiModelProperty(value = "人工补充预警")
    private Integer staffEarlyWarning;

    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;


}
