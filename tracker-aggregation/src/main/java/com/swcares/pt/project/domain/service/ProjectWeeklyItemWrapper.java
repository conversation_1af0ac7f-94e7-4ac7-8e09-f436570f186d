/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectWeeklyItemWrapper.java <br>
 * Package：com.swcares.pt.project.domain.service <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月18日 下午2:01:04 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.common.cons.Constants;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.domain.context.WeeklyContext;
import com.swcares.pt.project.domain.factory.WeeklyItemsFactory;
import com.swcares.pt.project.vo.ProjectMilestoneVO;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;
import com.swcares.pt.project.vo.WeeklyItemsVO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;

/**   
 * ClassName：com.swcares.pt.project.domain.service.ProjectWeeklyItemWrapper <br>
 * Description：项目周报明细项业务处理辅助类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月18日 下午2:01:04 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectWeeklyItemWrapper {

	/**
	 * Description：项目周报里程碑明细项处理 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 下午3:14:44 <br>
	 * @param weekly
	 * @param last
	 * @param ctx <br>
	 */
	public void applyWeeklyItem(WeeklyContext ctx) {
		List<ProjectMilestoneVO> milestones = ctx.getProjectMilestones();
		Map<String, WeeklyItemsVO> lastItems = gainLastItem(ctx);
		List<ProjectWeekPlanVO> wps = ctx.getWeekPlans();
		Map<String, List<ProjectWeekPlanVO>> milesWeekPlans = wps.stream().collect(Collectors.groupingBy(ProjectWeekPlanVO::getMilestoneCode));
		ProjectWeekly weekly = ctx.getWeekly();
		for (ProjectMilestoneVO m : milestones) {
			WeeklyItems item = WeeklyItemsFactory.create();
			BeanUtil.copyProperties(m, item, Constants.IGNORES_FIELD);
			WeeklyItemsVO lastItem = lastItems.get(m.getMilestoneCode());
			item.setLastWeekTotalEv((lastItem != null && lastItem.getWeekTotalEv() != null) ? lastItem.getWeekTotalEv() : new BigDecimal(0));
			ProjectWeekPlanVO plan = findMilestoneWeekPlan(milesWeekPlans, m, weekly);
			item.setWeekTotalPv(calculateWeekTotalPv(lastItem, plan));
			item.setPlanCode(ctx.getMonthPlanCode());
			ctx.addWeeklyItems(item);
		}
	}
	
	/**
	 * Description：找出里程碑的当前周报的周计划，如果没有就取里程碑的最后一周的周计划 <br>
	 * author：罗江林 <br>
	 * date：2025年6月11日 上午11:31:59 <br>
	 * @param plans 里程碑周计划
	 * @param m 里程碑
	 * @param weekly 周报
	 * @return <br>
	 */
	private ProjectWeekPlanVO findMilestoneWeekPlan(Map<String, List<ProjectWeekPlanVO>> plans, ProjectMilestoneVO m, ProjectWeekly weekly) {
		List<ProjectWeekPlanVO> datas = plans.get(m.getMilestoneCode());
		if(ListUtils.isEmpty(datas)) {
			return null;
		}
		List<ProjectWeekPlanVO> res = null;
		if(NumberUtil.equals(weekly.getYear(), m.getProjectYear())) {
			 res = datas.stream().filter(p -> compareCondition(p, weekly)).collect(Collectors.toList());
			 if(ListUtils.isEmpty(res)) {
				 ProjectWeekPlanVO last = findLastWeekPlan(datas);
				 if(last != null && last.getYearWeekNum().intValue() < weekly.getYearWeekNum().intValue()) {
					 return last;
				 }
			 }
		}
		if(ListUtils.isEmpty(res) && weekly.getYear().intValue() > m.getProjectYear().intValue()) {
			return findLastWeekPlan(datas);
		}
		res = datas.stream().filter(p -> NumberUtil.equals(p.getYearWeekNum(), weekly.getYearWeekNum())).collect(Collectors.toList());
		return ListUtils.isNotEmpty(res) ? res.get(0) : null;
	}
	
	private boolean compareCondition(ProjectWeekPlanVO p, ProjectWeekly weekly) {
		return NumberUtil.equals(p.getYear(), weekly.getYear()) 
				&& NumberUtil.equals(p.getYearWeekNum(), weekly.getYearWeekNum());
	}
	
	private ProjectWeekPlanVO findLastWeekPlan(List<ProjectWeekPlanVO> datas) {
		Comparator<? super ProjectWeekPlanVO> comparator = Comparator.comparing(ProjectWeekPlanVO::getStartDate);
		Optional<ProjectWeekPlanVO> max = datas.stream().max(comparator);
		return max.get();
	}
	
	private BigDecimal calculateWeekTotalPv(WeeklyItemsVO last, ProjectWeekPlanVO plan) {
		BigDecimal totalPv = new BigDecimal(0);
		if(plan == null && last != null) {
			totalPv = totalPv.add(last.getWeekTotalPv());
		}
		if(plan != null) {
			totalPv = totalPv.add(plan.getAccumulateWorkLoad());
		}
		return totalPv;
	}
	
	private Map<String, WeeklyItemsVO> gainLastItem(WeeklyContext ctx) {
		List<WeeklyItemsVO> lastItems = ctx.gainWeeklyItems();
		if(ListUtils.isEmpty(lastItems)) {
			return MapUtils.newHashMap();
		}
		return lastItems.stream().collect(Collectors.toMap(d -> d.getMilestoneCode(), v -> v));
	}
	
}
