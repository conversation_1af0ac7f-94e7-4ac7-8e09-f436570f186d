/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyContext.java <br>
 * Package：com.swcares.pt.project.domain.context <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月18日 上午8:51:10 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.context;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.common.cons.Constants;
import com.swcares.pt.common.model.LoginUser;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.enums.WeeklyStateEnum;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.WeekInfo;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.domain.factory.ProjectWeeklyFactory;
import com.swcares.pt.project.domain.service.ProjectWeeklyItemWrapper;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.repository.ProjectWeekPlanRepository;
import com.swcares.pt.project.vo.ProjectInfoVO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;
import com.swcares.pt.project.vo.WeeklyItemsVO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.swcares.pt.project.domain.context.WeeklyContext <br>
 * Description：项目周报上下文对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月18日 上午8:51:11 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class WeeklyContext {
	
	private ProjectWeekPlanRepository weekPlanRepository;
	
	private LoginUser user;
	private Map<String, ProjectInfoVO> projects;
	private List<ProjectWeekPlanVO> weekPlans;
	private Map<String, List<ProjectMilestoneVO>> milestones;
	private Map<Long, List<WeeklyItemsVO>> weeklyItems;
	private Integer year;
	private Integer weekSerial;
	private WeekInfo week;
	@Builder.Default
	private List<ProjectWeekly> currentWeeklys = ListUtils.newArrayList();
	@Builder.Default
	private List<ProjectWeeklyVO> lastWeeklys = ListUtils.newArrayList();
	private List<ProjectWeekPlanVO> projectWeekPlans;
	private ProjectWeeklyVO lastWeekly;
	private ProjectInfoVO projectInfo;
	private ProjectWeekly weekly;
	
	private BigDecimal weekTotalPv;
	
	public WeeklyContext initWeekDate(String date) {
		DateTime startDate = (StrUtil.isNotEmpty(date)) ? LdtUtils.customizeWeekStart(date) : 
			(weekSerial != null && year != null) ? LdtUtils.getWeekDate(year, weekSerial) : DateUtil.beginOfWeek(DateUtil.nextWeek());
		DateTime endDate = DateUtil.endOfWeek(startDate); 
		DateTime friday = DateUtil.endOfWeek(startDate).offset(DateField.DAY_OF_WEEK, -2);
		year = friday.year(); 
		weekSerial = friday.weekOfYear(); 
		week = WeekInfo.builder().startDate(startDate).endDate(endDate).friday(friday).year(year).weekSerial(weekSerial).build();
		return this;
	}
	
	public Map<String, List<ProjectWeekPlanVO>> gainProjectWeekPlans(){
		if(ListUtils.isNotEmpty(this.weekPlans)) {
			return this.weekPlans.stream().collect(Collectors.groupingBy(p -> p.getProjectCode()));
		}
		return MapUtils.newHashMap();
	}
	
	public Map<String, ProjectWeeklyVO> getLastProjectWeeklys(){
		if(ListUtils.isNotEmpty(this.lastWeeklys)) {
			return this.lastWeeklys.stream().collect(Collectors.toMap(p -> p.getProjectCode(), v -> v));
		}
		return MapUtils.newHashMap(); 
	}
	
	public List<ProjectMilestoneVO> getProjectMilestones(String pcode){
		return this.milestones != null && StrUtil.isNotEmpty(pcode) ? this.milestones.get(pcode) : ListUtils.newArrayList(); 
	}
	
	public List<ProjectMilestoneVO> getProjectMilestones(){
		String code = this.weekly != null ? this.weekly.getProjectCode() : null;
		return getProjectMilestones(code); 
	}
	
	public boolean doHaveMilestone(String pcode) {
		List<ProjectMilestoneVO> milestones = getProjectMilestones(pcode);
		return ListUtils.isNotEmpty(milestones);
	}
	
	public List<WeeklyItemsVO> gainWeeklyItems(){
		if(this.weeklyItems != null && lastWeekly != null) {
			return this.weeklyItems.get(lastWeekly.getId());
		}
		return ListUtils.newArrayList();
	}
	
	public String gainLastWeeklyProjectCode() {
		return lastWeekly != null ? lastWeekly.getProjectCode() : null;
	}
	
	public void addWeeklys() {
		this.weekly.setWeekTotalPv(this.weekTotalPv);
		this.currentWeeklys.add(this.weekly);
		this.projectWeekPlans = null;
		this.lastWeekly = null;
		this.projectInfo = null;
		this.weekTotalPv = null;
		this.weekly = null;
	}
	
	public List<String> getProjectCodes(){
		if(MapUtils.isNotEmpty(projects)) {
			return ListUtils.newArrayList(projects.keySet());
		}
		return ListUtils.newArrayList();
	}
	
	public String getMonthPlanCode() {
		return this.weekly.getMonthPlanCode();
	}
	
	public void addWeeklyItems(WeeklyItems item) {
		if(this.weekTotalPv == null) {
			this.weekTotalPv = initBigDecimal();
		}
		this.weekTotalPv = NumberUtil.add(this.weekTotalPv, item.getWeekTotalPv());
		this.weekly.addWeeklyItems(item);
	}
	
	public boolean valifyPlan(ProjectWeekPlanVO weekPlan) {
		if(this.projectWeekPlans != null && this.projectInfo != null && weekPlan != null) {
			return StrUtil.equals(weekPlan.getProjectCode(), projectInfo.getProjectCode());
		}
		return false;
	} 
	
	public void process(ProjectWeeklyItemWrapper wrapper) {
		Map<String, List<ProjectWeekPlanVO>> weekPlans = gainProjectWeekPlans();
		Map<String, ProjectWeeklyVO> lastWeeklys = getLastProjectWeeklys();
		for (String pcode : projects.keySet()) {
			if(!doHaveMilestone(pcode)) {
				log.info("【weekly】 {} 项目没有里程碑，不创建周报！---------->>", pcode);
				continue;
			}
			List<ProjectWeekPlanVO> plans = weekPlans.get(pcode);
			if(plans == null && MapUtils.isNotEmpty(weekPlans)) {
				ProjectWeekPlanVO other = weekPlans.get(ListUtils.newArrayList(weekPlans.keySet()).get(0)).get(0);
				plans = ListUtils.newArrayList(other);
			}
			if(lastWeeklys != null && lastWeeklys.size() > 0) {
				setLastWeekly(lastWeeklys.get(pcode));
			}
			setProjectWeekPlans(plans);
			setProjectInfo(projects.get(pcode));
			buildWeekly();
			wrapper.applyWeeklyItem(this);
			addWeeklys();
		}
	}
	
	private ProjectWeekPlanVO getWeekPlanVO() {
		if(ListUtils.isNotEmpty(projectWeekPlans)) {
			List<ProjectWeekPlanVO> temps = this.projectWeekPlans.stream().filter(p -> comparePlan(p)).collect(Collectors.toList());
			if(ListUtils.isNotEmpty(temps)) {
				return temps.get(0);
			}
		} 
		List<ProjectWeekPlanVO> wps = weekPlanRepository.getByCondition(ProjectWeekPlanDTO.builder().year(year).yearWeekNum(this.weekSerial).build());
		return ListUtils.isNotEmpty(wps) ? wps.get(0) : null;
	}
	
	private boolean comparePlan(ProjectWeekPlanVO p) {
		return NumberUtil.equals(p.getYear(), this.year) 
				&& NumberUtil.equals(p.getYearWeekNum(), this.weekSerial);
	}
	
	/**
	 * Description：根据上下文数据构建周报Domain对象 <br>
	 * author：罗江林 <br>
	 * date：2025年3月24日 下午5:42:42 <br>
	 * @return <br>
	 */
	public ProjectWeekly buildWeekly() {
		this.weekly = ProjectWeeklyFactory.create();
		int countor = 0;
		ProjectWeekPlanVO wpVO = getWeekPlanVO();
		BeanUtil.copyProperties(this.projectInfo, this.weekly, Constants.IGNORES_FIELD);
		if(this.lastWeekly != null) {
			BeanUtil.copyProperties(this.lastWeekly, this.weekly, ignores());
			this.weekly.setLastWeekTotalEv(this.lastWeekly.getWeekTotalEv());
			countor ++;
		}
		if(ListUtils.isNotEmpty(this.projectWeekPlans) && valifyPlan(wpVO) && countor == 0){
			BeanUtil.copyProperties(wpVO, this.weekly, Constants.IGNORES_FIELD);
			countor ++;
		} 
		if(countor == 0) {
			this.weekly.setWeekTotalPv(initBigDecimal());
		}
		if(wpVO != null) {
			if(valifyPlan(wpVO)) {
				this.weekly.setMonthPlanCode(wpVO.getMonthPlanCode());
				this.weekly.setWeekPlanCode(wpVO.getWeekPlanCode());
			}
			this.weekly.setStartDate(wpVO.getStartDate());
			this.weekly.setEndDate(wpVO.getEndDate());
			this.weekly.setWorkDays(wpVO.getWorkDays());
		} 
		this.weekly.setWeekMonth(week.getEndDate().toString(DatePattern.NORM_MONTH_PATTERN));
		this.weekly.setYear(getYear());
		this.weekly.setYearWeekNum(getWeekSerial());
		this.weekly.setWeeklyState(WeeklyStateEnum.not_write.getCode());
		this.weekly.setCurrentWeekEv(initBigDecimal());
		this.weekly.setPaceRisk(0);
		this.weekly.setWeekTotalEv(initBigDecimal());
		this.weekly.setDescription(null);
		this.weekly.setRemark(null);
//		this.weekly.setUser(user);
		return this.weekly;
	}
	
	private String[] ignores() {
		ArrayList<String> ignores = ListUtils.newArrayList(Constants.IGNORES_FIELD);
		ignores.add("currentWeekEv");
		ignores.add("weekTotalEv");
		ignores.add("fillInBy");
		ignores.add("fillInTime");
		ignores.add("paceRisk");
		ignores.add("paceOffset");
		ignores.add("description");
		ignores.add("remark");
		return ignores.toArray(new String[ignores.size()]);
	}
	
	private BigDecimal initBigDecimal() {
		return new BigDecimal(0.00);
	}
	
}
