package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.PagedDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectMonthPlanPagedDTO <br>
 * Description：里程碑月计划 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="MilestoneMonthPlanPagedDTO", description="里程碑月计划PagedDTO")
public class MilestoneMonthPlanPagedDTO extends PagedDTO implements Serializable{

	private static final long serialVersionUID = -2386913311165469715L;

	@ApiModelProperty(value = "里程碑编号")
	private String milestoneCode;
	
	@ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "项目年")
    private String projectYear;
    
    @ApiModelProperty(value = "计划月份")
    private String planMonth;
    
    @ApiModelProperty(value = "项目简称")
    private String projectAlias;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;
    
    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber; 

    @ApiModelProperty(value = "统计月份")
    private String statMonth;

    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;

}
