/**  
 * All rights Reserved, Designed By <br>
 * Title：StaffLargeScreenQuery.java <br>
 * Package：com.swcares.pt.project.query <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 上午9:52:17 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.query;

import java.util.List;

import com.swcares.pt.base.vo.StaffLargeScreenVO;
import com.swcares.pt.base.vo.StaffLoadAnalysisVO;
import com.swcares.pt.project.param.LargeScreenParam;

/**   
 * ClassName：com.swcares.pt.project.query.StaffLargeScreenQuery <br>
 * Description：大屏资源数据查询接口 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 上午9:52:17 <br>
 * @version v1.0 <br>  
 */
public interface StaffLargeScreenQuery {

	/**
	 * Description：资源总览查询 <br>
	 * author：罗江林 <br>
	 * date：2024年8月21日 上午9:51:20 <br>
	 * @param param
	 * @return <br>
	 */
	StaffLargeScreenVO searchStaffOverview(LargeScreenParam param);
	
	List<StaffLoadAnalysisVO> queryStaffAnalysis(LargeScreenParam param);
	
}
