/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectQueryParam.java <br>
 * Package：com.swcares.pt.project.param <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月26日 上午10:05:52 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.param;

import java.time.LocalDate;

import com.alibaba.excel.util.StringUtils;
import com.swcares.pt.core.util.PlmUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.param.QueryParam <br>
 * Description：项目查询参数 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月26日 上午10:05:52 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "QueryParam", description = "查询参数")
public class QueryParam {

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目年(2024)", example = "2024")
    private Integer projectYear;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;
    
    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;

    @ApiModelProperty(value = "统计月份(2024-08)", example = "2024-08")
    private String statMonth;
    
    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;
    
    /** 数据类型(1-负载，2-投入) */
    @ApiModelProperty(value = "数据类型(1-负载，2-投入)")
    private Integer dataType;
    
    @ApiModelProperty(value = "工号")
	private String jobNumber;
    
    @ApiModelProperty(value = "团队ID")
    private Long teamId;
    
    @ApiModelProperty(value = "在岗状态")
    private Long workState;
    
    public Integer getProjectYear() {
    	if(this.projectYear == null) {
    		this.projectYear = PlmUtil.getYear();
    	}
    	return this.projectYear;
    }
    
    public String getStatMonth() {
    	if(StringUtils.isEmpty(this.statMonth)) {
    		this.statMonth = PlmUtil.getYm();
    	}
    	return this.statMonth;
    }
	
}
