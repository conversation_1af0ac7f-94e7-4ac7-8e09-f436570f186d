package com.swcares.pt.project.vo;

import java.time.LocalDateTime;

import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectRiskTrackVO <br>
 * Description：项目风险VO <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectRiskTrackVO", description="项目风险VO")
public class ProjectRiskTrackVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "风险名称")
    private String riskName;

    @ApiModelProperty(value = "风险类型")
    private Integer riskType;

    @ApiModelProperty(value = "状态")
    private Integer riskState;

    @ApiModelProperty(value = "进展描述")
    private String description;

    @ApiModelProperty(value = "关闭日期")
    private LocalDateTime closeDate;


}
