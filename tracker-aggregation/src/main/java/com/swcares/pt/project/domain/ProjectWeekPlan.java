package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.common.util.StrUtils;
import com.swcares.pt.project.repository.ProjectWeekPlanRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectWeekPlan <br>
 * Description：项目周计划Domain <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectWeekPlan extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 周计划编号 */
    private String weekPlanCode;

    /** 月计划编号 */
    private String monthPlanCode;

    /** 项目编号 */
    private String projectCode;

    /** 里程碑编号 */
    private String milestoneCode;
    
    /** 年份 */
    private Integer year;

    /** 年度第几周 */
    private Integer yearWeekNum;

    /** 工作天数 */
    private Integer workDays;

    /** 计划工量(PV-人天) */
    private BigDecimal planWorkLoad;
    
    /** 累计工量(PV-人天) */
    private BigDecimal accumulateWorkLoad;

    /** 开始日期 */
    private LocalDate startDate;

    /** 结束日期 */
    private LocalDate endDate;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;

    private ProjectWeekPlanRepository repository;

	public ProjectWeekPlan(ProjectWeekPlanRepository repository) {
		this.repository = repository;
	}

	public static ProjectWeekPlan build(ProjectWeekPlanRepository repository) {
		return new ProjectWeekPlan(repository);
	}

	public boolean save() {
		return this.repository.save(this);
	}
	
	public void setWeekPlanCode() {
		this.weekPlanCode = this.monthPlanCode + "W"+ StrUtils.appendNum(yearWeekNum);
	}

}
