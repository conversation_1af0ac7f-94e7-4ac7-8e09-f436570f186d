package com.swcares.pt.project.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* ClassName：com.swcares.pt.project.pmt.vo.ProjectMonthPaceVO <br>
* Description：项目月进度返回展示对象 <br>
* Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
* Company：Aviation Cares Of Southwest Chen Du LTD  <br>
* <AUTHOR> <br>
* Date 2024-08-15 <br>
* @version v1.0 <br>
*/
@Data
@EqualsAndHashCode
@ApiModel(value="MonthPaceExportVO", description="项目月进度数据导出VO")
public class MonthPaceExportVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @ColumnWidth(20)
    @ExcelProperty(value = "项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ColumnWidth(20)
    @ExcelProperty(value = "项目编号")
    @ApiModelProperty(value = "项目编码")
    private String projectCode;
    
    @ApiModelProperty(value = "项目简称")
    private String projectAlias;

    @ApiModelProperty(value = "项目年")
    private Integer projectYear;
    
    @ColumnWidth(20)
    @ExcelProperty(value = "所属交付组")
    @ApiModelProperty(value = "交付组")
    private String deliveryTeam;
    
    @ColumnWidth(20)
    @ExcelProperty(value = "所属经营单元")
    @ApiModelProperty(value = "经营单元")
    private String managementUnit;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

    @ColumnWidth(10)
    @ExcelProperty(value = "统计截止月份")
    @ApiModelProperty(value = "统计月份")
    private String statMonth;

    @ColumnWidth(15)
    @ExcelProperty(value = "统计截止日期")
    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;

    @ColumnWidth(15)
    @ExcelProperty(value = "本月累计PV")
    @ApiModelProperty(value = "当月累计PV(人天)")
    private BigDecimal planWorkLoad;
    
    @ColumnWidth(15)
    @ExcelProperty(value = "本月累计EV")
    @ApiModelProperty(value = "当月累计EV(人天)")
    private BigDecimal workLoadTotal;

    @ColumnWidth(15)
    @ExcelProperty(value = "进度偏差率")
    @ApiModelProperty(value = "进度偏差")
    private String paceOffset;

    @ApiModelProperty(value = "里程碑滞后数量")
    private Integer milestoneOverdue;

    @ApiModelProperty(value = "人工补充预警")
    private String staffEarlyWarning;

    @ColumnWidth(10)
    @ExcelProperty(value = "进度风险")
    @ApiModelProperty(value = "进度风险")
    private String paceRisk;


}
