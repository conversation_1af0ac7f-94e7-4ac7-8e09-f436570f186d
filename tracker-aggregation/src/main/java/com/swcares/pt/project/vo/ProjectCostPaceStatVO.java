/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectCostPaceStatVO.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 下午4:48:59 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.vo.ProjectCostPaceStatVO <br>
 * Description：项目进度、成本统计VO <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 下午4:48:59 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProjectCostPaceStatVO", description = "大屏项目进度/成本统计对象")
public class ProjectCostPaceStatVO {

	@ApiModelProperty(value = "进度统计数据")
	private List<ProjectAnalysisStatVO> paces;
	
	@ApiModelProperty(value = "成本统计数据")
	private List<ProjectAnalysisStatVO> costs;
	
}
