/**  
 * All rights Reserved, Designed By <br>
 * Title：LargeScreenParam.java <br>
 * Package：com.swcares.pt.project.param <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 上午9:50:35 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.param;

import com.swcares.pt.core.util.PlmUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.param.LargeScreenParam <br>
 * Description：大屏统计查询参数 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 上午9:50:35 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "LargeScreenParam", description = "大屏统计查询参数")
public class LargeScreenParam {

	@ApiModelProperty(value = "所属年度(2024)", example = "2024")
    private Integer projectYear;
	
	@ApiModelProperty(value = "统计截止月份(2024-08)", example = "2024-08")
    private String statMonth;
	
	public Integer getProjectYear() {
    	if(this.projectYear == null) {
    		this.projectYear = PlmUtil.getYear();
    	}
    	return this.projectYear;
    }
	
}
