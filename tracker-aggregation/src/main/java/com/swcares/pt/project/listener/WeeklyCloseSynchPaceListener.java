/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyCloseSynchPaceListener.java <br>
 * Package：com.swcares.pt.project.event <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月26日 上午10:18:29 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.listener;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.cons.Constants;
import com.swcares.pt.project.domain.ProjectMonthPace;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.factory.ProjectMonthPaceFactory;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.repository.ProjectMonthPaceRepository;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.swcares.pt.project.event.WeeklyCloseSynchPaceListener <br>
 * Description：周报关闭事件监听同步更新月度进展 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月26日 上午10:18:29 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@Component
public class WeeklyCloseSynchPaceListener {

	@Autowired
	private ProjectMonthPaceRepository paceRepository;
	
	@EventListener(WeeklyStateEvent.class)
	public void weeklyCloseListener(WeeklyStateEvent event) {
		log.info("【WeeklyCloseListener】 WeeklyCloseEvent {}------------->>>");
		if(event.getWeekly() != null) {
			ProjectWeekly weekly = event.getWeekly();
			ProjectMonthPaceVO original = loadOriginal(weekly);
			ProjectMonthPace pace = ProjectMonthPaceFactory.create();
			BeanUtil.copyProperties(weekly, pace, Constants.IGNORES_FIELD);
			pace.synch(weekly, original);
			log.info("【Pace】 WeeklyCloseEvent {}------------->>>", weekly.getProjectCode() +"&"+ weekly.getWeekMonth());
		}
	}
	
	private ProjectMonthPaceVO loadOriginal(ProjectWeekly weekly) {
		List<ProjectMonthPaceVO> paces = paceRepository.getByCondition(QueryParam.builder().projectCode(weekly.getProjectCode())
				.statMonth(weekly.getWeekMonth())
				.projectYear(weekly.getYear())
				.build());
		if(ListUtils.isNotEmpty(paces)) {
			return paces.get(0);
		}
		return null;
	}
	
}
