/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectInfoParam.java <br>
 * Package：com.swcares.pt.project.param <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 上午9:13:15 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.param;

import com.swcares.pt.core.util.PlmUtil;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.param.ProjectInfoParam <br>
 * Description：项目信息参数对象 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月21日 上午9:13:15 <br>
 * @version v1.0 <br>  
 */
@Data
public class ProjectInfoParam {

    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目简称")
    private String projectAlias;
    
    @ApiModelProperty(value = "所属年度", example = "2024")
    private Integer projectYear;
    
    @ApiModelProperty(value = "项目性质")
    private Integer projectNature;
    
	@ApiModelProperty(value = "客户单位")
	private String customerName;
	
    @ApiModelProperty(value = "项目编码")
    private String projectSn;

    @ApiModelProperty(value = "所属经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "优先级")
    private Integer priorityLevel;

    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "项目阶段")
    private Integer projectStage;
    
    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;

    @ApiModelProperty(value = "成本风险")
    private Integer costRisk;
    
    
    public Integer getProjectYear() {
    	if(this.projectYear == null) {
    		this.projectYear = PlmUtil.getYear();
    	}
    	return this.projectYear;
    }
	
}
