/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectLargeScreenQuery.java <br>
 * Package：com.swcares.pt.project.query <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午4:45:56 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.query;

import java.util.List;

import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.vo.ManagementUnitRiskStatVO;
import com.swcares.pt.project.vo.ManagementUnitStatVO;
import com.swcares.pt.project.vo.ProjectCostPaceStatVO;
import com.swcares.pt.project.vo.ProjectLargeScreenVO;

/**   
 * ClassName：com.swcares.pt.project.query.ProjectLargeScreenQuery <br>
 * Description：大屏项目数据查询接口 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午4:45:56 <br>
 * @version v1.0 <br>  
 */
public interface ProjectLargeScreenQuery {

	/**
	 * Description：大屏项目总览数据 <br>
	 * author：罗江林 <br>
	 * date：2024年8月20日 下午4:52:31 <br>
	 * @param params
	 * @return <br>
	 */
	ProjectLargeScreenVO searchProjectOverview(ProjectInfoParam params);
	
	/**
	 * Description：项目进度、成本统计查询 <br>
	 * author：罗江林 <br>
	 * date：2024年8月21日 下午4:51:51 <br>
	 * @param param
	 * @return <br>
	 */
	ProjectCostPaceStatVO searchCostPaceStat(LargeScreenParam param);
	
	/**
	 * Description：经营单元进度统计分析 <br>
	 * author：罗江林 <br>
	 * date：2024年8月21日 下午3:32:30 <br>
	 * @param params
	 * @return <br>
	 */
	List<ManagementUnitStatVO> searchMgrUnitPaceStat(LargeScreenParam params);
	
	/**
	 * Description：经营单元成本统计分析 <br>
	 * author：罗江林 <br>
	 * date：2024年8月21日 下午3:32:30 <br>
	 * @param params
	 * @return <br>
	 */
	List<ManagementUnitStatVO> searchMgrUnitCostStat(LargeScreenParam params);
	
	
	/**
     * Description：查询经营单元成本/进度风险 <br>
     * author：罗江林 <br>
     * date：2024年8月22日 上午11:24:45 <br>
     * @param params
     * @return <br>
     */
    List<ManagementUnitRiskStatVO> queryProjectRiskStat(LargeScreenParam params);
	
}
