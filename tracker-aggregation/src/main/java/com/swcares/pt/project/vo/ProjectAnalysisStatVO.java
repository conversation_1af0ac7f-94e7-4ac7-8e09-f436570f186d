/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectAnalysisStatVO.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午1:20:49 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.ProjectAnalysisStatVO <br>
 * Description：项目分析统计VO <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午1:20:49 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value="ProjectAnalysisStatVO", description="项目分析统计VO")
public class ProjectAnalysisStatVO {

	@ApiModelProperty(value = "统计月份")
	private String statMonth;
	
	@ApiModelProperty(value = "计划编码工量")
    private BigDecimal planCodingWorkLoad;

    @ApiModelProperty(value = "完成编码工量")
    private BigDecimal finishCodingWorkLoad;
	
}
