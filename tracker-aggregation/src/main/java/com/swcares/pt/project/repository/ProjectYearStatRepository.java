package com.swcares.pt.project.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectYearStat;
import com.swcares.pt.project.dto.ProjectYearStatDTO;
import com.swcares.pt.project.vo.ProjectYearStatVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectWeekPlanRepository <br>
 * Description：项目年统计仓储接口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:04:36 <br>
 * @version v1.0 <br>  
 */
public interface ProjectYearStatRepository extends BaseRepository<ProjectYearStat, ProjectYearStatVO> {

	/**
	 * Description：根据查询条件加载项目年统计数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectYearStatVO> getByCondition(ProjectYearStatDTO params);
	
	
}
