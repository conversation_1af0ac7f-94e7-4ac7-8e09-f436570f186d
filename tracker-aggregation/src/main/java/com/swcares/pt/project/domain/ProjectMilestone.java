package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.common.date.DateHelper;
import com.swcares.pt.common.date.HolidayHelper;
import com.swcares.pt.common.date.MonthWorkdays;
import com.swcares.pt.common.date.YearHoliday;
import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectMilestoneRepository;
import com.swcares.pt.project.vo.ProjectInfoVO;

import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectMilestone <br>
 * Description：项目里程碑 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-12 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectMilestone extends BaseDomain {

    private static final long serialVersionUID = 1L;
    
    @Setter(value = AccessLevel.NONE)
    private static Map<Integer, YearHoliday> holidays = MapUtils.newHashMap();

    /** 里程碑编号 */
    private String milestoneCode;

    /** 里程碑名称 */
    private String milestoneName;

    /** 项目编号 */
    private String projectCode;
    
    /** 项目年 */
    private Integer projectYear;

    /** 经营单元 */
    private Long managementUnit;

    /** 交付组 */
    private Long deliveryTeam;
    
    /** 项目经理工号 */
    private String jobNumber;

    /** 开始日期 */
    private LocalDate startDate;

    /** 结束日期 */
    private LocalDate endDate;

    /** 工作日（天） */
    private Integer workDay;

    /** 计划工量（人天） */
    private BigDecimal planWorkLoad;

    /** 备注 */
    private String remark;
    
    
    private String holidayJson;
    
    private YearHoliday holiday;
    
    private List<MonthWorkdays> monthWorkdays;
    
    private BigDecimal costPrice;
    
    private List<MilestoneMonthPlan> milestoneMonthPlans;
    
    private List<ProjectMonthPlan> projectMonthPlans;
    
    private ProjectInfoVO projectInfo;

    private ProjectMilestoneRepository repository;

	public ProjectMilestone(ProjectMilestoneRepository repository) {
		this.repository = repository;
	}
    
    public static ProjectMilestone build(ProjectMilestoneRepository repository) {
    	return new ProjectMilestone(repository);
    }
    
    public boolean save() {
    	return this.repository.save(this);
    }
    
    public boolean remove() {
    	return this.repository.delMilestoneAndMonthPlanWeekPlan(this);
    }
    
    public boolean valifyRange() {
    	if(this.projectYear == null) {
    		this.projectYear = startDate.getYear();
    	}
    	return startDate.getYear() != endDate.getYear();
    }
    
    public int milestoneYear() {
    	return startDate.getYear();
    }
    
    public void addMonthPlan(MilestoneMonthPlan p) {
    	if(this.milestoneMonthPlans == null) {
    		this.milestoneMonthPlans = ListUtils.newArrayList();
    	}
    	this.milestoneMonthPlans.add(p);
    }
    
    public void matchHoliday(Map<Integer, String> hoildays) {
    	if(valifyRange()){
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, this.milestoneCode+"里程碑周期存在跨年！");
		}
    	String hoilday = hoildays.get(this.projectYear);
		if(StrUtil.isEmpty(hoilday)) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, this.milestoneCode+"里程碑"+startDate.getYear()+"法定假没有！");
		}
		this.holidayJson = hoilday;
    }
    
    public String gainStartDate() {
    	return this.startDate.toString();
    }
    
    public String gainEndDate() {
    	return this.endDate.toString();
    }

    public List<MilestoneMonthPlan> getMilestoneMonthPlans(){
    	if(this.milestoneMonthPlans == null) {
    		this.milestoneMonthPlans = ListUtils.newArrayList();
    	}
    	return this.milestoneMonthPlans;
    }
    
    /**
     * Description：初始化法定假,并根据里程碑起始日期进行拆分解析；同时初始化公共数据 <br>
     * author：罗江林 <br>
     * date：2025年3月28日 下午3:11:42 <br> <br>
     */
    public void complementMilestone() {
    	getHoliDays();
		if(this.projectInfo != null) {
			this.setDeliveryTeam(this.projectInfo.getDeliveryTeam());
			this.setManagementUnit(this.projectInfo.getManagementUnit());
			this.setJobNumber(this.projectInfo.getJobNumber());
		}
		if(this.holiday != null) {
			this.setProjectYear(holiday.getYear());
			this.monthWorkdays = DateHelper.handleMonthWorkdays(gainStartDate(), gainEndDate(), holiday);
		}
	}
    
    public YearHoliday getHoliDays() {
		Integer key = this.milestoneYear();
		this.holiday = holidays.get(key);
		if(this.holiday == null) {
			this.holiday = HolidayHelper.analysisHoliday(this.holidayJson);
			holidays.put(key, this.holiday);
		}
		this.holiday.setYear(key);
		this.holidayJson = null;
		return this.holiday;
    }
    
}
