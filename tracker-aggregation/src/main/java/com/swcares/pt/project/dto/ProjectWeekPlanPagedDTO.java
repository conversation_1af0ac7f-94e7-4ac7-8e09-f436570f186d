package com.swcares.pt.project.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import com.swcares.pt.core.DataScopeHelper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectWeekPlanPagedDTO <br>
 * Description：项目周计划 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectWeekPlanPagedDTO", description="项目周计划PagedDTO")
public class ProjectWeekPlanPagedDTO extends PagedDTO implements DataScopeHelper{

	@ApiModelProperty(value = "周计划编号")
    private String weekPlanCode;

    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

    @ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;
    
    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "年度第几周")
    private Integer yearWeekNum;

    @ApiModelProperty(value = "工作天数")
    private Integer workDays;

    @ApiModelProperty(value = "计划工量(PV-人天)")
    private BigDecimal planWorkLoad;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

}
