package com.swcares.pt.project.domain;

import java.math.BigDecimal;

import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.WeeklyItemsRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.WeeklyItems <br>
 * Description：周报项Domain <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WeeklyItems extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 周报ID */
    private Long weeklyId;

    /** 月计划编号 */
    private String planCode;

    /** 项目编号 */
    private String projectCode;

    /** 里程碑编号 */
    private String milestoneCode;

    /** 本周EV */
    private BigDecimal currentWeekEv;

    /** 上周累计EV */
    private BigDecimal lastWeekTotalEv;

    /** 累计EV */
    private BigDecimal weekTotalEv;

    /** 累计PV */
    private BigDecimal weekTotalPv;

    /** 进度偏差 */
    private BigDecimal paceOffset;
    
    /** 进度风险 */
    private Integer paceRisk;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;


    private WeeklyItemsRepository repository;

	public WeeklyItems(WeeklyItemsRepository repository) {
		this.repository = repository;
	}

	public static WeeklyItems build(WeeklyItemsRepository repository) {
		return new WeeklyItems(repository);
	}

	public boolean save() {
		return this.repository.save(this);
	}
	
	public boolean calculatePaceOffset(BigDecimal offsetThreshold) {
		if(this.weekTotalEv == null || this.weekTotalPv == null) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "计算【"+milestoneCode+"】里程碑进度偏差偏差异常，累计EV或PV为空!");
		}
//		this.paceOffset = this.weekTotalEv.divide(this.weekTotalPv.subtract(new BigDecimal(1)), 2 ,RoundingMode.HALF_UP);
		this.paceRisk = this.paceOffset.compareTo(offsetThreshold) <= 0 ? 1 : 0;
		return this.paceRisk == 1;
	}
    
}
