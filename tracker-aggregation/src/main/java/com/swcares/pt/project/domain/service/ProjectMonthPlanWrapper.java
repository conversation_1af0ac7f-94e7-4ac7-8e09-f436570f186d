/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPlanWrapper.java <br>
 * Package：com.swcares.pt.project.domain.service <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午4:09:58 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.common.cons.Constants;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.domain.context.MilestoneContext;
import com.swcares.pt.project.domain.factory.ProjectMonthPlanFactory;
import com.swcares.pt.project.dto.ProjectMonthPlanDTO;
import com.swcares.pt.project.repository.ProjectMonthPlanRepository;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

import cn.hutool.core.bean.BeanUtil;

/**   
 * ClassName：com.swcares.pt.project.domain.service.ProjectMonthPlanWrapper <br>
 * Description：项目月计划业务辅助处理类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午4:09:58 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectMonthPlanWrapper {

	@Autowired
	private ProjectWeekPlanWrapper weekPlanWrapper;
	@Autowired
	private ProjectMonthPlanRepository respository;
	
	/**
	 * Description：根据上下文对象进行里程碑月计划构建处理 <br>
	 * author：罗江林 <br>
	 * date：2025年3月14日 下午4:12:56 <br>
	 * @param ctx
	 * @return <br>
	 */
	public ProjectMilestone apply(MilestoneContext ctx) {
		return ctx.applyMonthPlan(weekPlanWrapper);
	}
	
	/**
	 * Description：计算出每个项目月计划 <br>
	 * author：罗江林 <br>
	 * date：2025年4月24日 上午11:39:24 <br>
	 * @param pms <br>
	 */
	public List<ProjectMonthPlan> projectMonthPlan(List<ProjectMilestone> pms) {
		List<MilestoneMonthPlan> mmps = ListUtils.newArrayList();
		pms.forEach(p -> {
			mmps.addAll(p.getMilestoneMonthPlans());
		});
		Map<String, ProjectMilestone> pmdatas = projectMilestone(pms);
//		Map<String, List<ProjectMonthPlanVO>> originals =loadOriginalData(ListUtils.newArrayList(pmdatas.keySet()));
		Map<String, List<MilestoneMonthPlan>> mps = mmps.stream().collect(Collectors.groupingBy(MilestoneMonthPlan::getProjectCode));
		List<ProjectMonthPlan> pmps = ListUtils.newArrayList();
		for (String pcode : mps.keySet()) {
			List<MilestoneMonthPlan> datas = mps.get(pcode);
			ProjectMilestone pm = pmdatas.get(pcode);
			projectMonthPlan(pm, pmps, datas);
		}
		return pmps;
	}
	
	/**
	 * Description：通过项目的里程碑月计划计算出项目月计划 <br>
	 * author：罗江林 <br>
	 * date：2025年4月24日 上午11:38:43 <br>
	 * @param pmps
	 * @param datas <br>
	 */
	private void projectMonthPlan(ProjectMilestone pm, List<ProjectMonthPlan> pmps, List<MilestoneMonthPlan> datas) {
		BigDecimal planWorkLoad = new BigDecimal(0);
		Map<String, List<MilestoneMonthPlan>> monthPlans = datas.stream().filter(p -> !p.delete()).collect(Collectors.groupingBy(MilestoneMonthPlan::getPlanMonth));
//		Map<String, List<ProjectMonthPlanVO>> monthOrigs = ListUtils.isNotEmpty(origs) ? origs.stream().collect(Collectors.groupingBy(ProjectMonthPlanVO::getPlanMonth)) : MapUtils.newHashMap();
		ArrayList<String> keys = ListUtils.newArrayList(monthPlans.keySet());
		Collections.sort(keys);
		List<ProjectMonthPlan> pmplans = ListUtils.newArrayList();
		for (String month : keys) {
			ProjectMonthPlan plan = null;
//			List<ProjectMonthPlanVO> poris = monthOrigs.get(month);
//			ProjectMonthPlanVO pvo = ListUtils.isNotEmpty(poris) ? poris.get(0) : null;
			List<MilestoneMonthPlan> mpdata = monthPlans.get(month);
			BigDecimal planMonthWorkLoad = new BigDecimal(0);
			for (MilestoneMonthPlan p : mpdata) { 
				if(plan == null) {
					plan = ProjectMonthPlanFactory.create();
					BeanUtil.copyProperties(p, plan, Constants.IGNORES_FIELD);
				}
				planMonthWorkLoad = planMonthWorkLoad.add(p.getPlanMonthWorkLoad());
			}
			planWorkLoad = planWorkLoad.add(planMonthWorkLoad);
			plan.setPlanMonthWorkLoad(planMonthWorkLoad);
			plan.setPlanWorkLoad(planWorkLoad);
//			if(pvo != null) {
//				plan.setId(pvo.getId());
//			}
			pmplans.add(plan);
			pmps.add(plan);
		}
		pm.setProjectMonthPlans(pmplans);
	}
	
	private Map<String, List<ProjectMonthPlanVO>> loadOriginalData(List<String> pcodes){
		Map<String, Object> params = MapUtils.newHashMap();
		params.put("pcodes", pcodes);
		List<ProjectMonthPlanVO> datas = respository.getByCondition(ProjectMonthPlanDTO.builder().params(params).build());
		return datas.stream().collect(Collectors.groupingBy(ProjectMonthPlanVO::getProjectCode));
	}
	
	private Map<String, ProjectMilestone> projectMilestone(List<ProjectMilestone> pms){
		Map<String, List<ProjectMilestone>> collect = pms.stream().collect(Collectors.groupingBy(ProjectMilestone::getProjectCode));
		Map<String, ProjectMilestone> pmms = MapUtils.newHashMap();
		for (String pcode : collect.keySet()) {
			List<ProjectMilestone> list = collect.get(pcode);
			pmms.put(pcode, list.get(0));
		}
		return pmms;
	}
	
}
