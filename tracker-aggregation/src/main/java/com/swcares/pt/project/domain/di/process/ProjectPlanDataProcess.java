/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectPlanDataProcessing.java <br>
 * Package：com.swcares.pt.di.impl <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月27日 上午11:48:38 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.di.process;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.cons.PlmErrors;
import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.di.DataImportBase;
import com.swcares.pt.di.DataImportProcess;
import com.swcares.pt.di.ImportContext;
import com.swcares.pt.enums.ProjectStateEnum;
import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.repository.ProjectMonthPlanRepository;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

import cn.hutool.core.text.StrPool;

/**   
 * ClassName：com.swcares.pt.di.impl.ProjectPlanDataProcessing <br>
 * Description：项目月计划数据导入处理 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月27日 上午11:48:38 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectPlanDataProcess extends DataImportBase<ProjectMonthPlan, ProjectMonthPlanVO>
		implements DataImportProcess<ProjectMonthPlan, ProjectMonthPlanVO> {

	@Autowired
	private ProjectMonthPlanRepository repository;

	@Override
	public List<ProjectMonthPlan> process(List<ProjectMonthPlan> sources) {
		List<ImportContext<ProjectMonthPlan, ProjectMonthPlanVO>> ctxs = groupingBuildContexts(sources);
		return mappingHandler(ctxs);
	}

	@Override
	public void findOriginalData(ImportContext<ProjectMonthPlan, ProjectMonthPlanVO> ctx) {
		Map<String, ProjectMonthPlanVO> originals = ctx.getOriginals().stream().collect(Collectors.toMap(c -> buildKey(c.getProjectCode(), c.getPlanMonth()), v -> v));
		ctx.getImportDatas().forEach(p -> {
			String key = buildKey(p.getProjectCode(), p.getPlanMonth());
			ProjectMonthPlanVO org = originals.get(key);
			if (org != null) {
				p.setId(org.getId());
			}
		});
	}

	@Override
	public List<ImportContext<ProjectMonthPlan, ProjectMonthPlanVO>> groupingBuildContexts(List<ProjectMonthPlan> datas) {
		Map<String, List<ProjectMonthPlan>> groupDatas = datas.stream().collect(Collectors.groupingBy(ProjectMonthPlan::getPlanMonth));
		List<ImportContext<ProjectMonthPlan, ProjectMonthPlanVO>> ctxs = ListUtils.newArrayList();
		groupDatas.keySet().forEach(key -> {
			List<ProjectMonthPlan> ds = groupDatas.get(key);
			// 验证数据重复性
			validateRepeat(ds, key);
			validatePorject(ds.stream().map(ProjectMonthPlan::getProjectCode).collect(Collectors.toList()), ProjectStateEnum.underway);
			ctxs.add(buildContext(ds));
		});
		return ctxs;
	}

	private void validateRepeat(List<ProjectMonthPlan> datas, String key) {
		try {
			datas.stream().collect(Collectors.toMap(ProjectMonthPlan::getProjectCode, d -> d));
		} catch (Exception e) {
			throw new BusinessException(PlmErrors.BDA_REPEAT_ERROR, key);
		}
	}

	private String buildKey(String code, String m) {
		return code + StrPool.AT + m;
	}

	@Override
	protected BaseRepository<ProjectMonthPlan, ProjectMonthPlanVO> getBaseRepository() {
		return this.repository;
	}

	@Override
	protected QueryParam buildParams(ProjectMonthPlan s) {
		return QueryParam.builder().statMonth(s.getPlanMonth()).build();
	}

}
