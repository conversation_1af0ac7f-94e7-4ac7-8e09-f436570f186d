/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectLevelVO.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午10:29:42 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.ProjectLevelVO <br>
 * Description：项目优先级分类VO <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午10:29:42 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value="ProjectPriorityStatVO", description="项目优先级统计VO")
public class ProjectPriorityStatVO {

	@ApiModelProperty(value = "项目总数量")
	private Integer totalQuantity;
	
	@ApiModelProperty(value = "一级")
	private Integer oneLevel;
	
	@ApiModelProperty(value = "二级")
	private Integer twoLevel;
	
	@ApiModelProperty(value = "三级")
	private Integer threeLevel;
	
	@ApiModelProperty(value = "四级")
	private Integer fourLevel;
	
}
