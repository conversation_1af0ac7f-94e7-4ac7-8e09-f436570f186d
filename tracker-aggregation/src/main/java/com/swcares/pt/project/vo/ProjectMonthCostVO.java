package com.swcares.pt.project.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* ClassName：com.swcares.pt.project.pmt.vo.ProjectMonthCostVO <br>
* Description：项目月成本返回展示对象 <br>
* Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
* Company：Aviation Cares Of Southwest Chen Du LTD  <br>
* <AUTHOR> <br>
* Date 2024-08-15 <br>
* @version v1.0 <br>
*/
@Data
@EqualsAndHashCode
@ApiModel(value="ProjectMonthCostVO", description="项目月成本")
public class ProjectMonthCostVO implements Serializable{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    
    @ApiModelProperty(value = "项目简称")
    private String projectAlias;

    @ApiModelProperty(value = "项目年")
    private String projectYear;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "统计月份")
    private String statMonth;

    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;

    @ApiModelProperty(value = "计划人工工量")
    private BigDecimal planWorkLoad;

    @ApiModelProperty(value = "计划当月编码产出")
    private BigDecimal planMonthCodingProduce;

    @ApiModelProperty(value = "计划编码工量")
    private BigDecimal planCodingWorkLoad;

    @ApiModelProperty(value = "当月编码成本")
    private BigDecimal monthCodingWorkCost;

    @ApiModelProperty(value = "编码使用成本")
    private BigDecimal finishCodingWorkCost;

    @ApiModelProperty(value = "成本偏差率")
    private BigDecimal costOffset;

    @ApiModelProperty(value = "人工补充预警")
    private Integer staffEarlyWarning;

    @ApiModelProperty(value = "成本风险")
    private Integer costRisk;

}
