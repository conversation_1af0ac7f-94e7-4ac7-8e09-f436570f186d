package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.repository.ProjectWeekPlanRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.ProjectWeekPlanFactory <br>
 * Description：项目周计划对象工厂类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectWeekPlanFactory {

	private static ProjectWeekPlanRepository repository;
	
	@Autowired
	public ProjectWeekPlanFactory(ProjectWeekPlanRepository repository) {
		ProjectWeekPlanFactory.repository = repository;
	}
	
	public static ProjectWeekPlan create() {
		return ProjectWeekPlan.build(repository);
	}
	
}
