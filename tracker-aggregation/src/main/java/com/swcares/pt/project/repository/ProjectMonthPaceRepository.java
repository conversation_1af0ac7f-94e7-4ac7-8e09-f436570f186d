/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPaceRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:52 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectMonthPace;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthPaceRepository <br>
 * Description：项目月进度仓储接口定义 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:52 <br>
 * @version v1.0 <br>  
 */
public interface ProjectMonthPaceRepository extends BaseRepository<ProjectMonthPace, ProjectMonthPaceVO> {

}
