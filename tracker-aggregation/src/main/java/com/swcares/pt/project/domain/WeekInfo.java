/**  
 * All rights Reserved, Designed By <br>
 * Title：WeekInfo.java <br>
 * Package：com.swcares.pt.project.domain <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年4月24日 上午9:56:40 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain;

import java.time.LocalDate;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.domain.WeekInfo <br>
 * Description：周信息 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年4月24日 上午9:56:40 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeekInfo {

	private DateTime startDate;
	
	private DateTime endDate;
	
	private DateTime friday;
	
	private Integer year;
	
	private Integer weekSerial;
	
	public WeekInfo getLastWeek() {
		DateTime last = DateUtil.offsetWeek(startDate, -1);
		DateTime bg = DateUtil.beginOfWeek(last);
		DateTime eg = DateUtil.endOfWeek(last);
		DateTime fd = DateUtil.endOfWeek(last).offset(DateField.DAY_OF_WEEK, -2);
		return WeekInfo.builder().startDate(bg).endDate(eg).year(fd.year()).friday(fd).weekSerial(fd.weekOfYear()).build();
	}
	
	public LocalDate gainStartDate() {
		return startDate != null ? startDate.toLocalDateTime().toLocalDate() : null;
	}
	
	public LocalDate gainEndDate() {
		return endDate != null ? endDate.toLocalDateTime().toLocalDate() : null;
	}
	
	public LocalDate gainFriday() {
		return friday != null ? friday.toLocalDateTime().toLocalDate() : null;
	}
	
}
