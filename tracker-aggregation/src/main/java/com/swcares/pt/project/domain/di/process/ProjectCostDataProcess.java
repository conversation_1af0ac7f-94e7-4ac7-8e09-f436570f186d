/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectCostDataProcessing.java <br>
 * Package：com.swcares.pt.di.impl <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月27日 上午11:48:38 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.di.process;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.cons.PlmErrors;
import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.di.DataImportBase;
import com.swcares.pt.di.DataImportProcess;
import com.swcares.pt.di.ImportContext;
import com.swcares.pt.enums.ProjectStateEnum;
import com.swcares.pt.project.domain.ProjectMonthCost;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.repository.ProjectMonthCostRepository;
import com.swcares.pt.project.vo.ProjectMonthCostVO;

import cn.hutool.core.text.StrPool;

/**   
 * ClassName：com.swcares.pt.di.impl.ProjectCostDataProcessing <br>
 * Description：项目成本数据导入处理 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月27日 上午11:48:38 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectCostDataProcess extends DataImportBase<ProjectMonthCost, ProjectMonthCostVO>
		implements DataImportProcess<ProjectMonthCost, ProjectMonthCostVO> {

	@Autowired
	private ProjectMonthCostRepository repository;

	@Override
	public List<ProjectMonthCost> process(List<ProjectMonthCost> sources) {
		List<ImportContext<ProjectMonthCost, ProjectMonthCostVO>> ctxs = groupingBuildContexts(sources);
		return mappingHandler(ctxs);
	}

	@Override
	public void findOriginalData(ImportContext<ProjectMonthCost, ProjectMonthCostVO> ctx) {
		Map<String, ProjectMonthCostVO> originals = ctx.getOriginals().stream().collect(Collectors.toMap(c -> buildKey(c.getProjectCode(), c.getProjectYear(), c.getStatMonth()), v -> v));
		ctx.getImportDatas().forEach(p -> {
			String key = buildKey(p.getProjectCode(), p.getProjectYear(), p.getStatMonth());
			ProjectMonthCostVO org = originals.get(key);
			if (org != null) {
				p.setId(org.getId());
			}
		});
	}

	@Override
	public List<ImportContext<ProjectMonthCost, ProjectMonthCostVO>>
			groupingBuildContexts(List<ProjectMonthCost> datas) {
		Map<String, List<ProjectMonthCost>> groupDatas = datas.stream().collect(Collectors.groupingBy(ProjectMonthCost::getStatMonth));
		List<ImportContext<ProjectMonthCost, ProjectMonthCostVO>> ctxs = ListUtils.newArrayList();
		groupDatas.keySet().forEach(key -> {
			List<ProjectMonthCost> ds = groupDatas.get(key);
			// 验证数据重复性
			validateRepeat(ds, key);
			validatePorject(ds.stream().map(ProjectMonthCost::getProjectCode).collect(Collectors.toList()), ProjectStateEnum.underway);
			ctxs.add(buildContext(ds));
		});
		return ctxs;
	}

	private void validateRepeat(List<ProjectMonthCost> datas, String key) {
		try {
			datas.stream().collect(Collectors.toMap(ProjectMonthCost::getProjectCode, d -> d));
		} catch (Exception e) {
			throw new BusinessException(PlmErrors.BDA_REPEAT_ERROR, key);
		}
	}

	private String buildKey(String code, String py, String m) {
		return code + StrPool.AT + py + StrPool.AT + m;
	}

	@Override
	protected BaseRepository<ProjectMonthCost, ProjectMonthCostVO> getBaseRepository() {
		return this.repository;
	}

	@Override
	protected QueryParam buildParams(ProjectMonthCost s) {
		return QueryParam.builder().statMonth(s.getStatMonth()).build();
	}

}
