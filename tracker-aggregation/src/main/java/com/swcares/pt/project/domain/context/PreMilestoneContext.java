/**  
 * All rights Reserved, Designed By <br>
 * Title：PreMilestoneContext.java <br>
 * Package：com.swcares.pt.project.domain.context <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月21日 下午1:27:18 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.context;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;

import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.cons.PlmErrors;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.vo.ProjectInfoVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.domain.context.PreMilestoneContext <br>
 * Description：里程碑数据导入前期准备上下文 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月21日 下午1:27:18 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreMilestoneContext {

	private List<ProjectMilestone> source;
	private Map<Integer, String> hoildays;
	private Map<Integer, BigDecimal> costPrice;
	private Map<String, ProjectInfoVO> projectInfos;
	
	public boolean hasSource() {
		return ListUtils.isEmpty(source);
	}
	
	public void process() {
		if(MapUtils.isEmpty(projectInfos) || ListUtils.isEmpty(source)) {
			return;
		}
		validateRepeat();
		source.forEach(pm -> { 
			ProjectInfoVO pi = projectInfos.get(pm.getProjectCode());
			if(pi == null) {
				throw new BusinessException(CommonErrors.CUSTOM_ERROR, "项目【"+pm.getProjectCode()+"】未在项目总览中，请核对！");
			}
			pm.setProjectInfo(pi);
			pm.matchHoliday(hoildays); 
			pm.setCostPrice(getYearCostPrice(pm.milestoneYear()));
		});
	}

	public BigDecimal getYearCostPrice(Integer year) {
		if(MapUtils.isEmpty(costPrice)) {
			return null;
		}
		return costPrice.get(year);
	}
	
	private void validateRepeat() {
		try {
			source.stream().collect(Collectors.toMap(ProjectMilestone::getMilestoneCode, d -> d));
		} catch (Exception e) {
			throw new BusinessException(PlmErrors.BDA_REPEAT_ERROR, "里程碑编号");
		}
	}
	
}
