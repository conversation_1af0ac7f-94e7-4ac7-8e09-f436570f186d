/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectInfoRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:04 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectRiskTrack;
import com.swcares.pt.project.dto.ProjectRiskTrackDTO;
import com.swcares.pt.project.vo.ProjectRiskTrackVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectInfoRepository <br>
 * Description：项目风险仓储接口定义  <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:04 <br>
 * @version v1.0 <br>  
 */
public interface ProjectRiskTrackRepository extends BaseRepository<ProjectRiskTrack, ProjectRiskTrackVO>{

	/**
	 * Description：根据查询条件加载项目风险数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectRiskTrackVO> getByCondition(ProjectRiskTrackDTO params);
	
	/**
	 * Description：获取指定项目编号所有风险集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月22日 下午4:22:45 <br>
	 * @param projectCode
	 * @return <br>
	 */
	List<ProjectRiskTrackVO> getRiskTrackByProjectCode(String projectCode);

	
}
