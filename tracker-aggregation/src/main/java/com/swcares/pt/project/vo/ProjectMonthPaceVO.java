package com.swcares.pt.project.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* ClassName：com.swcares.pt.project.pmt.vo.ProjectMonthPaceVO <br>
* Description：项目月进度返回展示对象 <br>
* Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
* Company：Aviation Cares Of Southwest Chen Du LTD  <br>
* <AUTHOR> <br>
* Date 2024-08-15 <br>
* @version v1.0 <br>
*/
@Data
@EqualsAndHashCode
@ApiModel(value="ProjectMonthPaceVO", description="项目月进度")
public class ProjectMonthPaceVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    
    @ApiModelProperty(value = "项目简称")
    private String projectAlias;

    @ApiModelProperty(value = "项目年")
    private Integer projectYear;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;
    
    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

    @ApiModelProperty(value = "统计月份")
    private String statMonth;

    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;

    @ApiModelProperty(value = "当月累计PV(人天)")
    private BigDecimal planWorkLoad;
    
    @ApiModelProperty(value = "当月累计EV(人天)")
    private BigDecimal workLoadTotal;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;

    @ApiModelProperty(value = "里程碑滞后数量")
    private Integer milestoneOverdue;

    @ApiModelProperty(value = "人工补充预警")
    private Integer staffEarlyWarning;

    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;


}
