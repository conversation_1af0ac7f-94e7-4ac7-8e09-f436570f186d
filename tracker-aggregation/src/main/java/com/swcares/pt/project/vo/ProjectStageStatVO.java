/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectStageStatVO.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午10:35:20 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.ProjectStageStatVO <br>
 * Description：项目阶段统计 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午10:35:20 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value="ProjectStageStatVO", description="项目阶段统计VO")
public class ProjectStageStatVO {
	
	@ApiModelProperty(value = "项目阶段")
	private Integer projectStage;

	@ApiModelProperty(value = "项目数量")
	private Integer projectCount; 
	
}
