/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectWeekPlanWrapper.java <br>
 * Package：com.swcares.pt.project.domain.service <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午4:17:49 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.service;

import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.context.MilestoneContext;

/**   
 * ClassName：com.swcares.pt.project.domain.service.ProjectWeekPlanWrapper <br>
 * Description：周计划业务逻辑辅助处理类<br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午4:17:49 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectWeekPlanWrapper {

	public void apply(MilestoneContext ctx) {
		ctx.applyWeekPlan();
	}
	
}
