/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMilestoneRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:04:36 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMilestoneRepository <br>
 * Description：里程碑仓储接口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:04:36 <br>
 * @version v1.0 <br>  
 */
public interface ProjectMilestoneRepository extends BaseRepository<ProjectMilestone, ProjectMilestoneVO> {

	/**
	 * Description：根据查询条件加载项目信息数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectMilestoneVO> getByCondition(ProjectMilestoneDTO params);
	
	/**
	 * Description：获取项目在建设中的所有里程碑数据 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 下午1:54:09 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectMilestoneVO> getByProjectState(ProjectMilestoneDTO params);
	
	/**
	 * Description：通过里程碑编号获取里程碑对象 <br>
	 * author：罗江林 <br>
	 * date：2025年4月7日 上午11:28:36 <br>
	 * @param milestoneCode
	 * @return <br>
	 */
	ProjectMilestoneVO getByMilestoneCode(String milestoneCode);
	
	/**
	 * Description：根据里程碑编号逻辑删除里程碑、月、周计划 <br>
	 * author：罗江林 <br>
	 * date：2025年3月17日 下午5:01:15 <br>
	 * @param p
	 * @return <br>
	 */
	boolean delMilestoneAndMonthPlanWeekPlan(ProjectMilestone p);

	/**
	 * Description：根据里程碑ID删除里程碑 <br>
	 * author：罗江林 <br>
	 * date：2025年4月7日 下午12:00:21 <br>
	 * @param m
	 * @return <br>
	 */
	boolean remove(ProjectMilestone m);
	
}
