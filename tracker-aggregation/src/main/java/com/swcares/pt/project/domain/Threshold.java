/**  
 * All rights Reserved, Designed By <br>
 * Title：Threshold.java <br>
 * Package：com.swcares.pt.project.domain <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月24日 下午1:11:50 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain;

import java.math.BigDecimal;

import lombok.Builder;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.domain.Threshold <br>
 * Description：阈值 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月24日 下午1:11:50 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
public class Threshold {

	/** 风险偏差阈值 */
    private BigDecimal offsetThreshold;
    
    /** 滞后阈值 */
    private Integer lagQuantity;
	
}
