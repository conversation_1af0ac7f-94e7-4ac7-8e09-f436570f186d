/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectInfoRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:04 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;
import java.util.Map;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.enums.ProjectStateEnum;
import com.swcares.pt.project.domain.ProjectInfo;
import com.swcares.pt.project.dto.ProjectInfoDTO;
import com.swcares.pt.project.vo.ProjectInfoVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectInfoRepository <br>
 * Description：项目信息仓储接口定义  <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:04 <br>
 * @version v1.0 <br>  
 */
public interface ProjectInfoRepository extends BaseRepository<ProjectInfo, ProjectInfoVO>{

	/**
	 * Description：根据项目ID获取返回领域对象 <br>
	 * author：罗江林 <br>
	 * date：2025年4月7日 上午11:53:28 <br>
	 * @param projectId
	 * @return <br>
	 */
	ProjectInfo getById(Long projectId);
	
	/**
	 * Description：根据查询条件加载项目信息数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectInfoVO> getByCondition(ProjectInfoDTO params);
	
	/**
	 * Description：获取指定项目年所有项目集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月22日 下午4:22:45 <br>
	 * @param year
	 * @return <br>
	 */
	Map<String, ProjectInfoVO> getProjectByState(ProjectStateEnum state);
	
	/**
	 * Description：加载所有建设中的项目 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 上午8:44:21 <br>
	 * @return <br>
	 */
	Map<String, ProjectInfoVO> getUnderwayProject();
	
	
	Map<String, ProjectInfoVO> getUnderwayProject(List<String> pcodes);
	
	/**
	 * Description：项目删除，验证项目是否存在已填写周报；同时删除该项目的里程碑、月、周计划和周报数据 <br>
	 * author：罗江林 <br>
	 * date：2025年3月21日 上午10:55:31 <br>
	 * @param pi
	 * @return <br>
	 */
	boolean removeProject(ProjectInfo pi);
	
}
