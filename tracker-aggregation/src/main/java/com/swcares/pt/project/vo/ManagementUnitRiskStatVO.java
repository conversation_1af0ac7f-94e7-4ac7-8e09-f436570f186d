/**  
 * All rights Reserved, Designed By <br>
 * Title：ManagementUnitPaceStat.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午1:25:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.ManagementUnitPaceStatVO <br>
 * Description：经营单元进度/成本风险VO <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午1:25:34 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value = "ManagementUnitRiskStatVO", description = "经营单元进度/成本风险VO")
public class ManagementUnitRiskStatVO {

	@ApiModelProperty(value = "经营单元")
	private String managementUnit;
	
	@ApiModelProperty(value = "经营单元名称")
	private String managementName;
	
	@ApiModelProperty(value = "项目数量")
	private Integer projectCount;
	
	@ApiModelProperty(value = "截止月份")
	private String statMonth;
	
	@ApiModelProperty(value = "进度风险数量")
    private Integer paceRiskCount;
	
	@ApiModelProperty(value = "成本风险数量")
    private Integer costRiskCount;
	
	@ApiModelProperty(value = "产出量缺口")
	private BigDecimal produceGap;
	
	@ApiModelProperty(value = "成本利用率")
	private BigDecimal costRate;

	
}
