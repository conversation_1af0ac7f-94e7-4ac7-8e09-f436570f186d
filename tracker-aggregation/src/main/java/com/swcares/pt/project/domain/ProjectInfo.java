package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectInfoRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectInfo <br>
 * Description：项目信息 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectInfo extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编码 */
    private String projectCode;

    /** 项目编号 */
    private String projectSn;

    /** 项目名称 */
    private String projectName;

    /** 项目简称 */
    private String projectAlias;

    /** 项目性质 */
    private Integer projectNature;

    /** 客户名称 */
    private String customerName;

    /** 经营单元 */
    private Long managementUnit;
    
    /** 交付组 */
    private Long deliveryTeam;

    /** 优先级 */
    private Integer priorityLevel;

    /** 工号 */
    private String jobNumber;

    /** 项目经理 */
    private String projectManager;

    /** 项目阶段 */
    private Integer projectStage;
    
    /** 项目状态 */
    private Integer projectState;

    /** 立项时间 */
    private LocalDate approvalDate;

    /** 计划启动时间 */
    private LocalDate planStartDate;

    /** 计划验收时间 */
    private LocalDate planEndDate;

    /** 计划结项时间 */
    private LocalDate planClosingDate;

    /** 预算内项目(0-否,1-是) */
    private Integer budgetProject;

    /** 税前收入 */
    private BigDecimal pretaxIncome;
    
    /** 税后收入 */
    private BigDecimal revenue;

    /** 备注 */
    private String remark;

    private ProjectInfoRepository repository;

	public ProjectInfo(ProjectInfoRepository repository) {
		this.repository = repository;
	}
	
	public static ProjectInfo build(ProjectInfoRepository repository) {
		return new ProjectInfo(repository);
	}
    
    public boolean save() {
    	return this.repository.save(this);
    }

    public boolean remove() {
    	return this.repository.removeProject(this);
    }
}
