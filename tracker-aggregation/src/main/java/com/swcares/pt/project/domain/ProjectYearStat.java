package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectYearStatRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectYearStat <br>
 * Description：项目年统计Domain <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectYearStat extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编号 */
    private String projectCode;

    /** 项目年度 */
    private Integer projectYear;

    /** 年度计划工量 */
    private BigDecimal planWorkLoad;

    /** 年度计划成本 */
    private BigDecimal yearPlanCost;

    /** 年度人工单价(元/人年) */
    private BigDecimal yearCostPrice;

    /** 备注 */
    private String remark;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;

    private ProjectYearStatRepository repository;
    
    private List<ProjectMilestone> milestones;

	public ProjectYearStat(ProjectYearStatRepository repository) {
		this.repository = repository;
	}

	public static ProjectYearStat build(ProjectYearStatRepository repository) {
		return new ProjectYearStat(repository);
	}

	public boolean save() {
		return this.repository.save(this);
	}

	public void setYearPlanCost() {
		if(yearCostPrice != null) {
			BigDecimal price = yearCostPrice.divide(new BigDecimal(12), 3, RoundingMode.HALF_UP).divide(new BigDecimal(20.83), 3, RoundingMode.HALF_UP);
			setYearPlanCost(price.multiply(planWorkLoad).setScale(3, RoundingMode.HALF_UP));
		}
	}
	
	public void process(String projectCode) {
		setProjectCode(projectCode);
		if(ListUtils.isEmpty(milestones)) {
			return;
		}
		BigDecimal total = new BigDecimal(0);
		for (ProjectMilestone m : milestones) {
			total = total.add(m.getPlanWorkLoad());
			if(getProjectYear() == null) {
				setProjectYear(m.getProjectYear());
			}
			if(getYearCostPrice() == null) {
				setYearCostPrice(m.getCostPrice());
			}
		} 
		setPlanWorkLoad(total);
		setYearPlanCost();
	}
	
}
