package com.swcares.pt.project.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.swcares.baseframe.common.base.entity.PagedDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectWeeklyPagedDTO <br>
 * Description：项目周报 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectWeeklyPagedDTO", description="项目周报PagedDTO")
public class ProjectWeeklyPagedDTO extends PagedDTO{

	@ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;

    @ApiModelProperty(value = "周计划编号")
    private String weekPlanCode;

    @ApiModelProperty(value = "所属月")
    private String weekMonth;
    
    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "年度第几周")
    private Integer yearWeekNum;

    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;

    @ApiModelProperty(value = "项目经理（工号）")
    private String jobNumber;
    
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "本周EV")
    private BigDecimal currentWeekEv;

    @ApiModelProperty(value = "累计EV")
    private BigDecimal weekTotalEv;

    @ApiModelProperty(value = "累计PV")
    private BigDecimal weekTotalPv;

    @ApiModelProperty(value = "上周累计EV")
    private BigDecimal lastWeekTotalEv;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;

    @ApiModelProperty(value = "跟踪人")
    private String trackedBy;

    @ApiModelProperty(value = "跟踪时间")
    private LocalDateTime trackedTime;

    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "状态(1-待填写,2-已填写,3-已关闭)")
    private Integer weeklyState;

    @ApiModelProperty(value = "进展描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

}
