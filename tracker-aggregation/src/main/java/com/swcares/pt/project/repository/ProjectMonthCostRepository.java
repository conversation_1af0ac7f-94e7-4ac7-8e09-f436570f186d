/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthCostRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:33 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectMonthCost;
import com.swcares.pt.project.vo.ProjectMonthCostVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthCostRepository <br>
 * Description：项目月成本仓储接口定义 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:47:33 <br>
 * @version v1.0 <br>  
 */
public interface ProjectMonthCostRepository extends BaseRepository<ProjectMonthCost, ProjectMonthCostVO> {
	
}
