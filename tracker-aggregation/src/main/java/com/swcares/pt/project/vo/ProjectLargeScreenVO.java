/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectLargeScreenVO.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午4:46:58 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.vo.ProjectLargeScreenVO <br>
 * Description：大屏项目总览数据对象 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午4:46:58 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProjectLargeScreenVO", description = "大屏项目总览数据对象")
public class ProjectLargeScreenVO {

	@ApiModelProperty(value = "项目总览")
	private ProjectOverviewVO overview;
	
	@ApiModelProperty(value = "项目分级")
	private ProjectPriorityStatVO priority;
	
	@ApiModelProperty(value = "项目阶段")
	private List<ProjectStageStatVO> stages;
	
	@ApiModelProperty(value = "交付工量")
	private List<DeliverWorkLoadStatVO> delivers;
	
}
