package com.swcares.pt.project.dto;

import javax.validation.constraints.NotEmpty;

import com.swcares.baseframe.common.base.entity.PagedDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectRiskTrackPagedDTO <br>
 * Description：项目风险 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectRiskTrackPagedDTO", description="项目风险PagedDTO")
public class ProjectRiskTrackPagedDTO extends PagedDTO{

	@NotEmpty(message = "项目编号参数不能为空")
	@ApiModelProperty(value = "项目编号")
    private String projectCode;

}
