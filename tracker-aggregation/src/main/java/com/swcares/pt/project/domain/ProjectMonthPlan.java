package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectMonthPlanRepository;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectMonthPlan <br>
 * Description：项目月计划Domain <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectMonthPlan extends BaseDomain {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "计划编号")
	private String planCode;

	@NotNull(message = "项目编号不能为空")
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
	
    @ApiModelProperty(value = "计划月份")
    private String planMonth; 
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber; 
    
    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "月工作日(天)")
    private Integer workDay;
    
    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;
    
    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "当月累计PV(人天)")
    private BigDecimal planWorkLoad;
    
    @ApiModelProperty(value = "当月PV(人天)")
    private BigDecimal planMonthWorkLoad;
    
    private Boolean deleted = false;
    
    private List<ProjectWeekPlan> weekPlans;

    private ProjectMonthPlanRepository repository;

	public ProjectMonthPlan(ProjectMonthPlanRepository repository) {
		this.repository = repository;
	}

	public boolean save() {
		return this.repository.save(this);
	}

	public static ProjectMonthPlan build(ProjectMonthPlanRepository repository) {
		return new ProjectMonthPlan(repository);
	}
	
	public void addWeekPlan(ProjectWeekPlan wp) {
		if(this.weekPlans == null) {
			this.weekPlans = ListUtils.newArrayList();
		}
		if(this.planMonthWorkLoad == null) {
			this.planMonthWorkLoad = new BigDecimal(0);
		}
		this.planMonthWorkLoad = this.planMonthWorkLoad.add(wp.getPlanWorkLoad());
		this.weekPlans.add(wp);
	}
	
	public List<ProjectWeekPlan> getWeekPlans(){
		if(this.weekPlans == null) {
			this.weekPlans = ListUtils.newArrayList();
		}
		return this.weekPlans;
	}
	
	public boolean delete() {
		return this.deleted;
	}

}
