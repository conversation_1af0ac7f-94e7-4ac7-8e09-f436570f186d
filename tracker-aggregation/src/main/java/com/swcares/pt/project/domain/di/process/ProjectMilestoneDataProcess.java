/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMilestoneDataProcess.java <br>
 * Package：com.swcares.pt.di.process <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:21:33 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.di.process;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.di.DataImportBase;
import com.swcares.pt.di.DataImportProcess;
import com.swcares.pt.di.ImportContext;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.service.ProjectMilestoneWrapper;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.repository.ProjectMilestoneRepository;
import com.swcares.pt.project.vo.ProjectMilestoneVO;

/**   
 * ClassName：com.swcares.pt.di.process.ProjectMilestoneDataProcess <br>
 * Description：里程碑导入业务逻辑实现 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:21:33 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectMilestoneDataProcess extends DataImportBase<ProjectMilestone, ProjectMilestoneVO>
		implements DataImportProcess<ProjectMilestone, ProjectMilestoneVO> {

	@Autowired
	private ProjectMilestoneRepository repository;
	@Autowired
	private ProjectMilestoneWrapper wrapper;

	@Override
	public List<ProjectMilestone> process(List<ProjectMilestone> sources) {
		List<ProjectMilestoneVO> originals = repository.getByCondition(ProjectMilestoneDTO.builder().milestoneCode(sources.get(0).getMilestoneCode()).build());
		ImportContext<ProjectMilestone, ProjectMilestoneVO> ctx = ImportContext.<ProjectMilestone, ProjectMilestoneVO>builder().originals(originals).importDatas(sources).build();
		if (ctx.isOriginal()) {
			findOriginalData(ctx);
		}
		wrapper.fission(ctx);
		return ctx.getImportDatas();
	}

	@Override
	public void findOriginalData(ImportContext<ProjectMilestone, ProjectMilestoneVO> ctx) {
		List<ProjectMilestoneVO> his = ctx.getOriginals();
		Map<String, ProjectMilestoneVO> originals = his.stream().collect(Collectors.toMap(p -> p.getMilestoneCode(), v -> v));
		ctx.getImportDatas().forEach(p -> {
			ProjectMilestoneVO org = originals.get(p.getMilestoneCode());
			if (org != null) {
				p.setId(org.getId());
			}
		});
	}

	@Override
	protected BaseRepository<ProjectMilestone, ProjectMilestoneVO> getBaseRepository() {
		return repository;
	}

	@Override
	protected QueryParam buildParams(ProjectMilestone s) {
		return QueryParam.builder().projectCode(s.getProjectCode()).build();
	}
	
}
