/**  
 * All rights Reserved, Designed By <br>
 * Title：MilestoneDelParam.java <br>
 * Package：com.swcares.pt.project.param <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年5月29日 下午4:02:39 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.param;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.param.MilestoneDelParam <br>
 * Description：里程碑导入，删除数据参数 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年5月29日 下午4:02:39 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "QueryParam", description = "查询参数")
public class MilestoneDelParam {
	
	/** 项目编号 */
	private List<String> pCodes;
	/** 里程碑编号 */
	private List<String> mCodes;
	/** 月计划编号 */
	private List<String> mpCodes;
	
}
