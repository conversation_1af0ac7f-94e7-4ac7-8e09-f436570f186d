package com.swcares.pt.project.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* ClassName：com.swcares.pt.project.pmt.vo.ProjectMilestoneVO <br>
* Description：项目里程碑返回展示对象 <br>
* Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
* Company：Aviation Cares Of Southwest Chen Du LTD  <br>
* <AUTHOR> <br>
* Date 2025-03-12 <br>
* @version v1.0 <br>
*/
@Data
@EqualsAndHashCode
public class MilestoneExportVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ColumnWidth(22)
    @ExcelProperty(value = "里程碑编号")
    private String milestoneCode;

    @ColumnWidth(20)
    @ExcelProperty(value = "里程碑名称")
    private String milestoneName;

    @ColumnWidth(22)
    @ExcelProperty(value = "项目编号")
    private String projectCode;
    
    @ColumnWidth(35)
    @ExcelProperty(value = "项目名称")
    private String projectName;
    
    @ExcelIgnore
    private Integer projectYear;

    @ColumnWidth(20)
    @ExcelProperty(value = "所属经营单元")
    private String managementUnit;

    @ColumnWidth(20)
    @ExcelProperty(value = "所属交付组")
    private String deliveryTeam;
    
    @ExcelIgnore
    private String jobNumber;

    @ColumnWidth(20)
    @ExcelProperty(value = "里程碑开始日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private LocalDate startDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "里程碑结束日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private LocalDate endDate;
    
    @ColumnWidth(24)
    @ExcelProperty(value = "计划人工工量(人日)")
    private BigDecimal planWorkLoad;

    @ColumnWidth(10)
    @ExcelProperty(value = "工作日")
    private Integer workDay;

    @ColumnWidth(35)
    @ExcelProperty(value = "备注")
    private String remark;

}
