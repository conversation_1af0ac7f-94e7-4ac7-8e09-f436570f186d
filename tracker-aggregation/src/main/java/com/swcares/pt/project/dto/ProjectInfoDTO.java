package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.alibaba.excel.annotation.ExcelProperty;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.baseframe.utils.collect.MapUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectInfoDTO <br>
 * Description：项目信息 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@ApiModel(value="ProjectInfoDTO", description="项目信息")
public class ProjectInfoDTO extends BaseEntity implements BaseDTO, Serializable {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @NotNull(message = "项目编码不能为空")
	@ExcelProperty("项目编号")
    @Length(max = 64)
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ExcelProperty(value = "项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;

	@ExcelProperty(value = "项目简称")
    @ApiModelProperty(value = "项目简称")
    private String projectAlias;
    
	@ExcelProperty(value = "客户单位")
	@ApiModelProperty(value = "客户单位")
	private String customerName;
	
    @ApiModelProperty(value = "项目编码")
    private String projectSn;

	@ExcelProperty(value = "项目性质")
    @ApiModelProperty(value = "项目性质")
    private Integer projectNature;

	@NotNull(message = "所属经营单元不能为空")
	@ExcelProperty(value = "所属经营单元")
    @ApiModelProperty(value = "所属经营单元")
    private Long managementUnit;
	
	@NotNull(message = "所属交付组不能为空")
	@ExcelProperty(value = "所属交付组")
	@ApiModelProperty(value = "所属交付组")
    private Long deliveryTeam;

	@ExcelProperty(value = "优先级")
    @ApiModelProperty(value = "优先级")
    private Integer priorityLevel;

	@NotNull(message = "项目经理工号不能为空")
	@ExcelProperty(value = "项目经理工号")
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

	@ExcelProperty(value = "项目经理")
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

	@ExcelProperty(value = "项目阶段")
    @ApiModelProperty(value = "项目阶段")
    private Integer projectStage;
	
	@ExcelProperty(value = "内部立项时间")
    @ApiModelProperty(value = "内部立项时间")
    private LocalDate approvalDate;

	@ExcelProperty(value = "计划启动时间")
    @ApiModelProperty(value = "计划启动时间")
    private LocalDate planStartDate;

	@ExcelProperty(value = "计划验收时间")
    @ApiModelProperty(value = "计划验收时间")
    private LocalDate planEndDate;

	@ExcelProperty(value = "计划结项时间")
    @ApiModelProperty(value = "计划结项时间")
    private LocalDate planClosingDate;

	@ExcelProperty(value = "是否预算内项目")
    @ApiModelProperty(value = "预算内项目(0-否,1-是)")
    private Integer budgetProject;

    @ApiModelProperty(value = "税前收入")
    private BigDecimal pretaxIncome;
    
	@ExcelProperty(value = "税后收入")
    @ApiModelProperty(value = "税后收入")
    private BigDecimal revenue;
	
	@ExcelProperty(value = "项目状态")
	@ApiModelProperty(value = "项目状态")
    private Integer projectState;
	
	protected Map<String, Object> params;
    
    public Map<String, Object> getParams(){
    	if(MapUtils.isEmpty(params)) {
    		params = MapUtils.newHashMap();
    	}
    	return params;
    }

}
