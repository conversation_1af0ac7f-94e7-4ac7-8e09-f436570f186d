/**  
 * All rights Reserved, Designed By <br>
 * Title：ManagementUnitPaceStat.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午1:25:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.ManagementUnitPaceStatVO <br>
 * Description：经营单元进度统计VO <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 下午1:25:34 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value = "ManagementUnitMonthStatVO", description = "经营单元月(进度/成本)统计VO")
public class ManagementUnitMonthStatVO {

	@ApiModelProperty(value = "经营单元")
	private Long managementUnit;
	
	@ApiModelProperty(value = "经营单元名称")
	private String managementName;
	
	@ApiModelProperty(value = "统计月份")
	private String statMonth;
	
	@ApiModelProperty(value = "进度符合度/成本利用率")
    private BigDecimal paceRate;

    @ApiModelProperty(value = "产出量缺口/成本超支")
    private BigDecimal actualFinish;
	
}
