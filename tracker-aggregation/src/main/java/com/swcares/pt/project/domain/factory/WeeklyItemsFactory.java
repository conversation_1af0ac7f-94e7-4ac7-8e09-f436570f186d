package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.repository.WeeklyItemsRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.WeeklyItemsFactory <br>
 * Description：项目周报明细项对象工厂类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class WeeklyItemsFactory {

	private static WeeklyItemsRepository repository;
	
	@Autowired
	public WeeklyItemsFactory(WeeklyItemsRepository repository) {
		WeeklyItemsFactory.repository = repository;
	}
	
	public static WeeklyItems create() {
		return WeeklyItems.build(repository);
	}
	
}
