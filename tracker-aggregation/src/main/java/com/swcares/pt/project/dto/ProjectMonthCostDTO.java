package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectMonthCostDTO <br>
 * Description：项目月成本 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
@ApiModel(value="ProjectMonthCostDTO", description="项目月成本")
public class ProjectMonthCostDTO extends BaseEntity implements BaseDTO, Serializable {

	private static final long serialVersionUID = -6874943893263230146L;

	@ApiModelProperty(value = "主键ID")
    private Long id;

	@NotNull(message = "项目编号不能为空")
    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目年")
    private String projectYear;
    
    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @NotNull(message = "统计月份不能为空")
    @ApiModelProperty(value = "统计月份")
    private String statMonth;

    @ApiModelProperty(value = "统计日期")
    private LocalDate statDate;

    @ApiModelProperty(value = "计划人工工量")
    private BigDecimal planWorkLoad;

    @ApiModelProperty(value = "计划当月编码产出")
    private BigDecimal planMonthCodingProduce;

    @ApiModelProperty(value = "计划编码工量")
    private BigDecimal planCodingWorkLoad;

    @ApiModelProperty(value = "当月编码成本")
    private BigDecimal monthCodingWorkCost;

    @ApiModelProperty(value = "编码使用成本")
    private BigDecimal finishCodingWorkCost;

    @ApiModelProperty(value = "成本偏差率")
    private BigDecimal costOffset;

    @ApiModelProperty(value = "人工补充预警")
    private Integer staffEarlyWarning;

    @ApiModelProperty(value = "成本风险")
    private Integer costRisk;

}
