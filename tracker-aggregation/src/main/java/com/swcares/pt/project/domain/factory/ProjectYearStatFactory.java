package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectYearStat;
import com.swcares.pt.project.repository.ProjectYearStatRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.ProjectYearStatFactory <br>
 * Description：项目年统计对象工厂类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectYearStatFactory {

	private static ProjectYearStatRepository repository;
	
	@Autowired
	public ProjectYearStatFactory(ProjectYearStatRepository repository) {
		ProjectYearStatFactory.repository = repository;
	}
	
	public static ProjectYearStat create() {
		return ProjectYearStat.build(repository);
	}
	
}
