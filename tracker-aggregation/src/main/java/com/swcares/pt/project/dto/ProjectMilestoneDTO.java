package com.swcares.pt.project.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import javax.validation.constraints.NotNull;

import com.alibaba.excel.annotation.ExcelProperty;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectMilestoneDTO <br>
 * Description：项目里程碑DTO <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-12 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectMilestoneDTO", description="项目里程碑DTO")
public class ProjectMilestoneDTO extends BaseEntity implements BaseDTO, Serializable {
	
	private static final long serialVersionUID = -1275421339313723235L;

	@NotNull(message = "里程碑编号不能为空")
	@ExcelProperty(value = "里程碑编号")
    @ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;

    @NotNull(message = "里程碑名称不能为空")
    @ExcelProperty(value = "里程碑")
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    @NotNull(message = "项目编号不能为空")
    @ExcelProperty(value = "项目编号")
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "项目年")
    private Integer projectYear;

    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;
    
    @ApiModelProperty(value = "项目经理工号")
    private String jobNumber;

    @NotNull(message = "开始日期不能为空")
    @ExcelProperty(value = "开始日期")
    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @NotNull(message = "结束日期不能为空")
    @ExcelProperty(value = "结束日期")
    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "工作日（天）")
    private Integer workDay;

    @NotNull(message = "计划人工工量不能为空")
    @ExcelProperty(value = "计划人工工量(人天)")
    @ApiModelProperty(value = "计划工量（人天）")
    private BigDecimal planWorkLoad;

    @ExcelProperty(value = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;


}
