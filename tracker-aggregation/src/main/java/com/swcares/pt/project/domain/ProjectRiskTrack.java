package com.swcares.pt.project.domain;

import java.time.LocalDateTime;

import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectRiskTrackRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectRiskTrack <br>
 * Description：项目风险Domain <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectRiskTrack extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编号 */
    private String projectCode;

    /** 风险名称 */
    private String riskName;

    /** 风险类型 */
    private Integer riskType;

    /** 状态 */
    private Integer riskState;

    /** 进展描述 */
    private String description;

    /** 关闭日期 */
    private LocalDateTime closeDate;

    /** 删除状态(1-删除,0-未删除) */
    private Boolean deleted;

    private ProjectRiskTrackRepository repository;

	public ProjectRiskTrack(ProjectRiskTrackRepository repository) {
		this.repository = repository;
	}

	public static ProjectRiskTrack build(ProjectRiskTrackRepository repository) {
		return new ProjectRiskTrack(repository);
	}

	public boolean save() {
		return this.repository.save(this);
	}

}
