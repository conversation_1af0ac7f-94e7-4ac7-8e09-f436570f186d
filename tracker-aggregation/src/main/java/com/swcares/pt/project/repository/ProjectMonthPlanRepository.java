/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPlanRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:48:07 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.dto.ProjectMonthPlanDTO;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthPlanRepository <br>
 * Description：项目月计划仓储接口定义 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:48:07 <br>
 * @version v1.0 <br>  
 */
public interface ProjectMonthPlanRepository extends BaseRepository<ProjectMonthPlan, ProjectMonthPlanVO>{
	
	/**
	 * Description：根据查询条件加载项目月计划数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectMonthPlanVO> getByCondition(ProjectMonthPlanDTO params);
	
}
