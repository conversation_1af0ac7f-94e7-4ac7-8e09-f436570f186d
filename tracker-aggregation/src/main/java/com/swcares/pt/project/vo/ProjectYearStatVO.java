package com.swcares.pt.project.vo;

import java.math.BigDecimal;

import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectYearStatVO <br>
 * Description：项目年统计 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectYearStatVO", description="项目年统计VO")
public class ProjectYearStatVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目年度")
    private String projectYear;
    
    @ApiModelProperty(value = "年度计划工量")
    private BigDecimal planWorkLoad;
    
    @ApiModelProperty(value = "年度计划成本")
    private BigDecimal yearPlanCost;

    @ApiModelProperty(value = "年度人工单价(元/人年)")
    private BigDecimal yearCostPrice;

    @ApiModelProperty(value = "备注")
    private String remark;

}
