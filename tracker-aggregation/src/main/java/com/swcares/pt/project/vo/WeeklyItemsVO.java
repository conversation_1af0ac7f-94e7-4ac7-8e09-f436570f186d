package com.swcares.pt.project.vo;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.WeeklyItemsVO <br>
 * Description：周报项 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="WeeklyItemsVO", description="周报项VO")
public class WeeklyItemsVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "周报ID")
    private Long weeklyId;

    @ApiModelProperty(value = "月计划编号")
    private String planCode;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;
    
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;
    
    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "计划工量（人天）")
    private BigDecimal planWorkLoad;

    @ApiModelProperty(value = "本周EV")
    private BigDecimal currentWeekEv;

    @ApiModelProperty(value = "上周累计EV")
    private BigDecimal lastWeekTotalEv;

    @ApiModelProperty(value = "累计EV")
    private BigDecimal weekTotalEv;

    @ApiModelProperty(value = "累计PV")
    private BigDecimal weekTotalPv;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;
    
    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;

}
