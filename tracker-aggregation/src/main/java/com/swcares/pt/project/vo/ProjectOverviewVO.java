/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectOverview.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午10:22:23 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.ProjectOverviewVO <br>
 * Description：项目总览 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午10:22:23 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value="ProjectOverviewVO", description="项目总览")
public class ProjectOverviewVO {
	
	@ApiModelProperty(value = "统计年份")
	private String statYear;

	@ApiModelProperty(value = "项目总数量")
	private Integer totalQuantity;
	
	@ApiModelProperty(value = "预计人工成本")
	private BigDecimal planWorkLoad;
	
	@ApiModelProperty(value = "预计编码人工成本")
	private BigDecimal codingWorkCost;
	
}
