/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectDeliverWorkLoadStat.java <br>
 * Package：com.swcares.pt.project.vo <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午11:49:48 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * ClassName：com.swcares.pt.project.vo.DeliverWorkLoadStatVO <br>
 * Description：交付工量统计VO <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月20日 上午11:49:48 <br>
 * @version v1.0 <br>  
 */
@Data
@ApiModel(value="DeliverWorkLoadStatVO", description="交付工量统计VO")
public class DeliverWorkLoadStatVO {

	@ApiModelProperty(value = "统计日期")
	private String statDate;
	
	@ApiModelProperty(value = "计划人工工量")
    private BigDecimal planWorkLoad;
	
	@ApiModelProperty(value = "计划编码工量")
    private BigDecimal planCodingWorkLoad;
	
}
