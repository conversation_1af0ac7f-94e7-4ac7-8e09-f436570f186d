/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMilestoneFactory.java <br>
 * Package：com.swcares.pt.project.domain.factory <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.repository.ProjectMilestoneRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.ProjectMilestoneFactory <br>
 * Description：里程碑对象工厂类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectMilestoneFactory {

	private static ProjectMilestoneRepository repository;
	
	@Autowired
	public ProjectMilestoneFactory(ProjectMilestoneRepository repository) {
		ProjectMilestoneFactory.repository = repository;
	}
	
	public static ProjectMilestone create() {
		return ProjectMilestone.build(repository);
	}
	
}
