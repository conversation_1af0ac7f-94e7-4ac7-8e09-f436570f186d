package com.swcares.pt.project.dto;

import java.io.Serializable;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import com.swcares.pt.core.DataScopeHelper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.ProjectInfoPagedDTO <br>
 * Description：项目信息 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectInfoPagedDTO", description="项目信息PagedDTO")
public class ProjectInfoPagedDTO extends PagedDTO implements DataScopeHelper, Serializable{

	private static final long serialVersionUID = -208753041118167982L;

	@ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "项目年")
    private Integer projectYear;

    @ApiModelProperty(value = "项目编号")
    private String projectSn;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目简称")
    private String projectAlias;

    @ApiModelProperty(value = "项目性质")
    private Integer projectNature;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;
    
    @ApiModelProperty(value = "所属交付组")
    private Long deliveryTeam;

    @ApiModelProperty(value = "优先级")
    private Integer priorityLevel;

    @ApiModelProperty(value = "工号")
    private String jobNumber;

    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "项目阶段")
    private Integer projectStage;
    
    @ApiModelProperty(value = "项目状态")
    private Integer projectState;
    
    @ApiModelProperty(value = "统计截止日期")
    private String statDate;
    
    @ApiModelProperty(value = "预算内项目(0-否,1-是)")
    private Integer budgetProject;
    
    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;

    @ApiModelProperty(value = "成本风险")
    private Integer costRisk;
    
}
