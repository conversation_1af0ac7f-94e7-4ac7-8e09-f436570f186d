package com.swcares.pt.project.dto;

import java.math.BigDecimal;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.baseframe.utils.collect.MapUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.WeeklyItemsDTO <br>
 * Description：周报项 <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="WeeklyItemsDTO", description="周报项DTO")
public class WeeklyItemsDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "周报ID")
    private Long weeklyId;

    @ApiModelProperty(value = "月计划编号")
    private String planCode;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;

    @ApiModelProperty(value = "本周EV")
    private BigDecimal currentWeekEv;

    @ApiModelProperty(value = "上周累计EV")
    private BigDecimal lastWeekTotalEv;

    @NotNull(message = "累计EV不能为空")
    @ApiModelProperty(value = "累计EV")
    private BigDecimal weekTotalEv;

    @NotNull(message = "累计PV不能为空")
    @ApiModelProperty(value = "累计PV")
    private BigDecimal weekTotalPv;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;
    
    private Map<String, Object> params;
    
    public Map<String, Object> getParams(){
    	if(MapUtils.isEmpty(params)) {
    		params = MapUtils.newHashMap();
    	}
    	return params;
    }

}
