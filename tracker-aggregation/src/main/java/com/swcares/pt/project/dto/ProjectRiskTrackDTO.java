package com.swcares.pt.project.dto;

import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import com.swcares.baseframe.common.base.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectRiskTrackDTO <br>
 * Description：项目风险DTO <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectRiskTrackDTO", description="项目风险DTO")
public class ProjectRiskTrackDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "ID不能为空")
    private Long id;
    
    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "风险名称")
    private String riskName;

    @ApiModelProperty(value = "风险类型")
    private Integer riskType;

    @ApiModelProperty(value = "状态")
    private Integer riskState;

    @ApiModelProperty(value = "进展描述")
    private String description;

    @ApiModelProperty(value = "关闭日期")
    private LocalDateTime closeDate;


}
