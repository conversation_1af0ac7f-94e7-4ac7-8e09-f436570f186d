package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.repository.ProjectWeeklyRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.ProjectWeeklyFactory <br>
 * Description：项目周报对象工厂类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectWeeklyFactory {

	private static ProjectWeeklyRepository repository;
	
	@Autowired
	public ProjectWeeklyFactory(ProjectWeeklyRepository repository) {
		ProjectWeeklyFactory.repository = repository;
	}
	
	public static ProjectWeekly create() {
		return ProjectWeekly.build(repository);
	}
	
}
