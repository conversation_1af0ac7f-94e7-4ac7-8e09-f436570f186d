package com.swcares.pt.project.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.dto.WeeklyItemsPagedDTO <br>
 * Description：周报项 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="WeeklyItemsPagedDTO", description="周报项PagedDTO")
public class WeeklyItemsPagedDTO extends PagedDTO{


}
