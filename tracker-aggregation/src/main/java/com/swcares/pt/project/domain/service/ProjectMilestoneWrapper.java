/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMilestoneTransferService.java <br>
 * Package：com.swcares.pt.project.domain.service <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午1:58:46 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.di.ImportContext;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.domain.assembler.MilestoneMonthPlanAssembler;
import com.swcares.pt.project.domain.assembler.WeekPlanAssembler;
import com.swcares.pt.project.domain.context.MilestoneContext;
import com.swcares.pt.project.dto.MilestoneMonthPlanDTO;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.repository.MilestoneMonthPlanRepository;
import com.swcares.pt.project.repository.ProjectWeekPlanRepository;
import com.swcares.pt.project.vo.MilestoneMonthPlanVO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**   
 * ClassName：com.swcares.pt.project.domain.service.ProjectMilestoneWrapper <br>
 * Description：项目里程碑业务辅助处理类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午1:58:46 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectMilestoneWrapper {
	
	@Autowired
	private ProjectMonthPlanWrapper monthPlanWrapper;
	@Autowired
	private MilestoneMonthPlanRepository milestoneMonthPlanRepository;
	@Autowired
	private ProjectWeekPlanRepository weekPlanRepository;
	
	/**
	 * Description：根据里程碑数据及日期，按月、周计划进行拆分并进行相应运算处理 <br>
	 * author：罗江林 <br>
	 * date：2025年3月14日 下午2:58:17 <br>
	 * @param ctx <br>
	 */
	public void fission(ImportContext<ProjectMilestone, ProjectMilestoneVO> ctx) {
		if(ListUtils.isEmpty(ctx.getImportDatas())) {
			return;
		}
		List<ProjectMilestone> datas = ctx.getImportDatas();
		List<ProjectMilestone> rs = ListUtils.newArrayList();
		for (ProjectMilestone pm : datas) {
			rs.add(applyMilestone(pm));
		}
		monthPlanWrapper.projectMonthPlan(rs);
		ctx.setImportDatas(rs);
	}
	
	
	/**
	 * Description：里程碑分解处理 <br>
	 * author：罗江林 <br>
	 * date：2025年3月17日 下午4:48:22 <br>
	 * @param pm
	 * @return <br>
	 */
	public ProjectMilestone applyMilestone(ProjectMilestone pm) {
		pm.complementMilestone();
		MilestoneContext ctx = MilestoneContext.builder().milestone(pm).monthWorkdays(pm.getMonthWorkdays()).build();
		monthPlanWrapper.apply(ctx);
//		handleOriginalDatas(ctx);
		pm = ctx.getMilestone();
		pm.setWorkDay(ctx.getMilestoneWorkDays().intValue());
		return pm;
	}
	
	protected void handleOriginalDatas(MilestoneContext ctx) {
		findMonthPlanOriginalData(ctx);
		findWeekPlanOriginalData(ctx);
	}
	
	/**
	 * Description：处理周计划历史数据 <br>
	 * author：罗江林 <br>
	 * date：2025年3月17日 下午4:49:02 <br>
	 * @param ctx <br>
	 */
	private void findWeekPlanOriginalData(MilestoneContext ctx) {
		ProjectMilestone m = ctx.getMilestone();
		List<ProjectWeekPlanVO> his = weekPlanRepository.getByCondition(ProjectWeekPlanDTO.builder().milestoneCode(m.getMilestoneCode()).build());
		Map<String, ProjectWeekPlanVO> originals = his.stream().collect(Collectors.toMap(p -> p.getWeekPlanCode(), v -> v));
		List<String> matched = ListUtils.newArrayList();
		m.getMilestoneMonthPlans().forEach(p -> {
			List<ProjectWeekPlan> wps = p.getWeekPlans();
			if(ListUtils.isNotEmpty(wps)) {
				wps.forEach(w -> {
					ProjectWeekPlanVO wp = originals.get(w.getWeekPlanCode());
					if(wp != null) {
						w.setId(wp.getId());
						matched.add(w.getWeekPlanCode());
					}
				});
			}
		});
		
		handlWeekPlanMatchingFailed(m, matched, originals);
	}
	
	private void handlWeekPlanMatchingFailed(ProjectMilestone m, List<String> matched, Map<String, ProjectWeekPlanVO> originals) {
		matched.forEach(key -> { originals.remove(key); });
		if(MapUtils.isNotEmpty(originals)) {
			Map<String, MilestoneMonthPlan> plans = m.getMilestoneMonthPlans().stream().collect(Collectors.toMap(p -> p.getPlanCode(), v -> v));
			originals.values().forEach(w -> {
				ProjectWeekPlan domain = WeekPlanAssembler.INSTANCE.toDomain(w, m.getUser());
				domain.setDeleted(Boolean.TRUE);
				plans.get(w.getMonthPlanCode()).getWeekPlans().add(domain);
			});
		}
	}
	
	/**
	 * Description：处理月计划历史数据 <br>
	 * author：罗江林 <br>
	 * date：2025年3月17日 下午4:49:39 <br>
	 * @param ctx <br>
	 */
	private void findMonthPlanOriginalData(MilestoneContext ctx) {
		ProjectMilestone m = ctx.getMilestone();
		List<MilestoneMonthPlanVO> his = milestoneMonthPlanRepository.getByCondition(MilestoneMonthPlanDTO.builder().milestoneCode(m.getMilestoneCode()).build());
		Map<String, MilestoneMonthPlanVO> originals = his.stream().collect(Collectors.toMap(p -> p.getPlanCode(), v -> v));
		List<String> matched = ListUtils.newArrayList();
		m.getMilestoneMonthPlans().forEach(p -> {
			MilestoneMonthPlanVO mp = originals.get(p.getPlanCode());
			if(mp != null) {
				p.setId(mp.getId());
				matched.add(p.getPlanCode());
			}
		});
		
		handlMonthPlanMatchingFailed(m, matched, originals);
	} 
	
	private void handlMonthPlanMatchingFailed(ProjectMilestone m, List<String> matched, Map<String, MilestoneMonthPlanVO> originals) {
		matched.forEach(key -> { originals.remove(key); });
		if(MapUtils.isNotEmpty(originals)) {
			originals.values().forEach(p -> {
				MilestoneMonthPlan domain = MilestoneMonthPlanAssembler.INSTANCE.toDomain(p, m.getUser());
				domain.setDeleted(Boolean.TRUE);
				m.getMilestoneMonthPlans().add(domain);
			});
		}
	} 
	
}
