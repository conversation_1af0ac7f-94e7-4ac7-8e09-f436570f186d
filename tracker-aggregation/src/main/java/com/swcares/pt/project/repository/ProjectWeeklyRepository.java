package com.swcares.pt.project.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.param.ProjectWeeklyParam;
import com.swcares.pt.project.vo.ProjectWeeklyDetailVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectWeeklyRepository <br>
 * Description：项目周报仓储接口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:04:36 <br>
 * @version v1.0 <br>  
 */
public interface ProjectWeeklyRepository extends BaseRepository<ProjectWeekly, ProjectWeeklyVO> {

	/**
	 * Description：获取指定ID周报及明细项 <br>
	 * author：罗江林 <br>
	 * date：2025年3月20日 下午3:20:17 <br>
	 * @param weeklyId
	 * @return <br>
	 */
	ProjectWeeklyDetailVO getByWeelyId(Long weeklyId);
	
	/**
	 * Description：获取指定ID周报 <br>
	 * author：罗江林 <br>
	 * date：2025年3月24日 上午10:05:50 <br>
	 * @param weeklyId
	 * @return <br>
	 */
	ProjectWeeklyVO getById(Long weeklyId);
	
	/**
	 * Description：根据项目编号、年份和第几周来加载周报 <br>
	 * author：罗江林 <br>
	 * date：2025年3月20日 下午4:33:08 <br>
	 * @param projectCode
	 * @param year
	 * @param weekNum
	 * @return <br>
	 */
	ProjectWeeklyVO getWeekly(String projectCode, Integer year, Integer weekNum);
	
	/**
	 * Description：加载项目的已填写或已关闭周报 <br>
	 * author：罗江林 <br>
	 * date：2025年3月19日 下午2:41:43 <br>
	 * @param p
	 * @return <br>
	 */
	List<ProjectWeeklyVO> loadWeeklys(String projectCode);
	
	/**
	 * Description：根据查询条件加载项目周报数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectWeeklyVO> getByCondition(ProjectWeeklyParam params);
	
	/**
	 * Description：根据查询条件加载项目周报及明细项的数据集 <br>
	 * author：罗江林 <br>
	 * date：2025年3月19日 下午1:43:49 <br>
	 * @param params
	 * @return <br>
	 */
	List<ProjectWeeklyDetailVO> getByConditionContainItem(ProjectWeeklyParam params);
	
	/**
	 * Description：批量保存或更新项目周报和明细项数据集 <br>
	 * author：罗江林 <br>
	 * date：2025年3月18日 下午5:58:48 <br>
	 * @param weeklys
	 * @return <br>
	 */
	boolean batchSaveWeeklys(List<ProjectWeekly> weeklys);
	
}
