/**  
 * All rights Reserved, Designed By <br>
 * Title：MilestoneContext.java <br>
 * Package：com.swcares.pt.project.domain.context <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午2:18:29 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.context;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.date.MonthWorkdays;
import com.swcares.pt.common.date.WeekWorkday;
import com.swcares.pt.common.util.LdtUtils;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.domain.factory.MilestoneMonthPlanFactory;
import com.swcares.pt.project.domain.factory.ProjectWeekPlanFactory;
import com.swcares.pt.project.domain.service.ProjectWeekPlanWrapper;

import cn.hutool.core.bean.BeanUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**   
 * ClassName：com.swcares.pt.project.domain.context.MilestoneContext <br>
 * Description：里程碑上下文对象 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月14日 下午2:18:29 <br>
 * @version v1.0 <br>  
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MilestoneContext {

	private ProjectMilestone milestone;
	private List<MonthWorkdays> monthWorkdays;
	private List<WeekWorkday> weeks;
	private MilestoneMonthPlan milestoneMonthPlan;
	private BigDecimal milestoneWorkDays;
	private BigDecimal weekWorkLoadTotal;
	
	@Builder.Default
	private BigDecimal accumulateWorkLoad = new BigDecimal(0);
	
	public BigDecimal getMilestoneWorkDays() {
		if(ListUtils.isEmpty(monthWorkdays)) {
			return new BigDecimal(0);
		}
		if(milestoneWorkDays == null) {
			milestoneWorkDays = new BigDecimal(monthWorkdays.stream().mapToInt(m -> m.getWorkDays()).sum());
		}
		return milestoneWorkDays;
	}
	
	public void clear() {
		this.milestoneMonthPlan = null;
//		this.weekWorkLoadTotal = new BigDecimal(0);
	}
	
	public void addAccumulateWorkLoad(BigDecimal monthWorkLoad) {
		this.accumulateWorkLoad = this.accumulateWorkLoad.add(monthWorkLoad);
	}
	
	public void addWeekWorkLoadTotal(BigDecimal workLoad) {
		if(this.weekWorkLoadTotal == null) {
			this.weekWorkLoadTotal = new BigDecimal(0);
		}
		this.weekWorkLoadTotal = this.weekWorkLoadTotal.add(workLoad);
	}
	
	public void addMonthPlan() {
		if(this.milestoneMonthPlan != null && this.milestone != null) {
			this.milestone.addMonthPlan(milestoneMonthPlan);
		}
		clear();
	}
	
	/**
	 * Description：根据拆分月份及周工作创建月计划和周计划 <br>
	 * author：罗江林 <br>
	 * date：2025年3月25日 上午9:22:12 <br>
	 * @param wrapper
	 * @return <br>
	 */
	public ProjectMilestone applyMonthPlan(ProjectWeekPlanWrapper wrapper) {
		if(ListUtils.isEmpty(this.monthWorkdays)) {
			return null;
		}
		Collections.sort(this.monthWorkdays);
		for (MonthWorkdays wd : this.monthWorkdays) {
			setWeeks(wd.getWeeks());
			buildMonthPlan(wd);
			wrapper.apply(this);
			accumulate();
			addMonthPlan();
		}
		return this.milestone;
	}
	
	private MilestoneMonthPlan buildMonthPlan(MonthWorkdays w) {
		MilestoneMonthPlan domain = MilestoneMonthPlanFactory.create();
		BeanUtil.copyProperties(this.milestone, domain, "id", "repository", "weekPlans", "startDate", "endDate");
		BeanUtil.copyProperties(w, domain); 
		domain.setPlanMonth(w.getMonth());
		domain.setWorkDay(w.getWorkDays());
		domain.setMonthPlanCode(w.getSerial());
		setMilestoneMonthPlan(domain);
		return domain;
	}
	
	private void accumulate(){
		addAccumulateWorkLoad(this.milestoneMonthPlan.getPlanMonthWorkLoad());
		this.milestoneMonthPlan.setPlanWorkLoad(getAccumulateWorkLoad());
	}
	
	/**
	 * Description：构建周计划 <br>
	 * author：罗江林 <br>
	 * date：2025年3月25日 上午9:23:31 <br> <br>
	 */
	public void applyWeekPlan() {
		if(ListUtils.isEmpty(this.weeks)) {
			return;
		}
		Collections.sort(this.weeks);
		for (WeekWorkday w : this.weeks) {
			buildWeekPlan(w);
		}
	}
	
	private ProjectWeekPlan buildWeekPlan(WeekWorkday w) {
		BigDecimal loads = mathWeekWorkLoad(w);
		addWeekWorkLoadTotal(loads);
		ProjectWeekPlan domain = ProjectWeekPlanFactory.create();
		BeanUtil.copyProperties(this.milestone, domain, "id", "repository", "startDate", "endDate");
		domain.setYear(w.getFriday().year());
		domain.setWorkDays(w.getWorkCount());
		domain.setMonthPlanCode(this.milestoneMonthPlan.getPlanCode());
		domain.setStartDate(LdtUtils.convert(w.getStartDate()));
		domain.setEndDate(LdtUtils.convert(w.getEndDate()));
		domain.setYearWeekNum(w.getSerial());
		domain.setAccumulateWorkLoad(getWeekWorkLoadTotal());
		domain.setPlanWorkLoad(loads);
		domain.setWeekPlanCode();
		
		this.milestoneMonthPlan.addWeekPlan(domain);
		return domain;
	}
	
	private BigDecimal mathWeekWorkLoad(WeekWorkday w) {
		BigDecimal wl = this.milestone.getPlanWorkLoad();
		BigDecimal totalDays = getMilestoneWorkDays();
		BigDecimal avg = wl.divide(totalDays, 2, RoundingMode.HALF_UP);
		BigDecimal wds = new BigDecimal(w.getWorkCount());
		return wds.multiply(avg).setScale(2, RoundingMode.HALF_UP);
	}
	
}
