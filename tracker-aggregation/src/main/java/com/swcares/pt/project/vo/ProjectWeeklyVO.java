package com.swcares.pt.project.vo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONField;
import com.swcares.baseframe.common.base.entity.BaseEntity;

import cn.hutool.core.date.DatePattern;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectWeeklyVO <br>
 * Description：项目周报VO <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectWeeklyVO", description="项目周报VO")
public class ProjectWeeklyVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;

    @ApiModelProperty(value = "周计划编号")
    private String weekPlanCode;

    @ApiModelProperty(value = "年份")
    private Integer year;
    
    @ApiModelProperty(value = "所属月")
    private String weekMonth;
    
    @ApiModelProperty(value = "年度第几周")
    private Integer yearWeekNum;

    @ApiModelProperty(value = "工作天数")
    private Integer workDays;

    @ApiModelProperty(value = "经营单元")
    private Long managementUnit;

    @ApiModelProperty(value = "交付组")
    private Long deliveryTeam;

    @ApiModelProperty(value = "项目经理（工号）")
    private String jobNumber;
    
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    @ApiModelProperty(value = "本周EV")
    private BigDecimal currentWeekEv;

    @ApiModelProperty(value = "累计EV")
    private BigDecimal weekTotalEv;

    @ApiModelProperty(value = "累计PV")
    private BigDecimal weekTotalPv;

    @ApiModelProperty(value = "上周累计EV")
    private BigDecimal lastWeekTotalEv;

    @ApiModelProperty(value = "进度偏差")
    private BigDecimal paceOffset;

    @ApiModelProperty(value = "填报人")
    private String fillInBy;

    @ApiModelProperty(value = "填报时间")
    private LocalDateTime fillInTime;

    @ApiModelProperty(value = "进度风险")
    private Integer paceRisk;
    
    @ApiModelProperty(value = "风险数量")
    private Integer riskNum;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @JSONField(format = DatePattern.NORM_DATETIME_PATTERN)
    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;
    
    @ApiModelProperty(value = "周报周期")
    private String cycle;

    @ApiModelProperty(value = "状态(1-待填写,2-已填写,3-已关闭)")
    private Integer weeklyState;

    @ApiModelProperty(value = "进展描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark; 
    
    public String getCycle() {
    	return startDate.toString() + "~" + endDate.toString();
    }

}
