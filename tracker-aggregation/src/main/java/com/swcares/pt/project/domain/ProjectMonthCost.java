package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectMonthCostRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectMonthCost <br>
 * Description：项目月成本 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectMonthCost extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编码 */
    private String projectCode;

    /** 项目年 */
    private String projectYear;
    
    private Long managementUnit;

    /** 统计月份 */
    private String statMonth;

    /** 统计日期 */
    private LocalDate statDate;
    
    /** 计划人工工量 */
    private BigDecimal planWorkLoad;

    /** 计划当月编码产出 */
    private BigDecimal planMonthCodingProduce;

    /** 计划编码工量 */
    private BigDecimal planCodingWorkLoad;

    /** 当月编码成本 */
    private BigDecimal monthCodingWorkCost;

    /** 编码使用成本 */
    private BigDecimal finishCodingWorkCost;

    /** 成本偏差率 */
    private BigDecimal costOffset;

    /** 人工补充预警 */
    private Integer staffEarlyWarning;

    /** 成本风险 */
    private Integer costRisk;

    private ProjectMonthCostRepository repository;

	public ProjectMonthCost(ProjectMonthCostRepository repository) {
		this.repository = repository;
	}
	
	public static ProjectMonthCost build(ProjectMonthCostRepository repository) {
		return new ProjectMonthCost(repository);
	}
    
    public boolean save() {
    	return this.repository.save(this);
    }

}
