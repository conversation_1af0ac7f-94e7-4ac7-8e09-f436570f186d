/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyAssembler.java <br>
 * Package：com.swcares.pt.project.assembler <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:41:51 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.assembler;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.LoginUser;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.factory.ProjectWeeklyFactory;
import com.swcares.pt.project.dto.ProjectWeeklyDTO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

/**   
 * ClassName：com.swcares.pt.project.assembler.WeeklyAssembler <br>
 * Description：ProjectWeekly域对象转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:41:51 <br>
 * @version v1.0 <br>  
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
		nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WeeklyAssembler {

	WeeklyAssembler INSTANCE = Mappers.getMapper(WeeklyAssembler.class);
	
	default ProjectWeekly toDomain(ProjectWeeklyDTO dto, LoginUser user) {
		ProjectWeekly state = ProjectWeeklyFactory.create();
		INSTANCE.update(dto, user, state);
		return state;
	}
	
	default ProjectWeekly toDomain(ProjectWeeklyVO vo, LoginUser user) {
		ProjectWeekly state = ProjectWeeklyFactory.create();
		INSTANCE.update(vo, user, state);
		return state;
	}
	
	default ProjectWeeklyVO toVO(ProjectWeekly weekly) {
		ProjectWeeklyVO vo = new ProjectWeeklyVO();
		INSTANCE.update(weekly, vo);
		return vo;
	}
	
	default List<ProjectWeekly> toDomains(List<ProjectWeeklyDTO> states, LoginUser user){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<ProjectWeekly> datas = ListUtils.newArrayList();
		states.forEach(s -> {
			datas.add(WeeklyAssembler.INSTANCE.toDomain(s, user));
		});
		return datas;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "deleted", ignore = true)
	@Mapping(target = "last", ignore = true)
	@Mapping(target = "threshold", ignore = true)
	@Mapping(target = "isClose", ignore = true)
	@Mapping(source = "dto.id", target = "id")
	@Mapping(source = "dto.jobNumber", target = "jobNumber")
	@Mapping(target = "items", expression = "java(com.swcares.pt.project.domain.assembler.WeeklyItemsAssembler.INSTANCE.toDomains(dto.getItems(), user))")
	void update(ProjectWeeklyDTO dto, LoginUser user, @MappingTarget ProjectWeekly state);
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "deleted", ignore = true)
	@Mapping(target = "items", ignore = true)
	@Mapping(target = "last", ignore = true)
	@Mapping(target = "threshold", ignore = true)
	@Mapping(target = "isClose", ignore = true)
	@Mapping(source = "vo.id", target = "id")
	@Mapping(source = "vo.jobNumber", target = "jobNumber")
	void update(ProjectWeeklyVO vo, LoginUser user, @MappingTarget ProjectWeekly state);
	
	@Mapping(target = "cycle", ignore = true)
	@Mapping(target = "projectName", ignore = true)
	@Mapping(target = "riskNum", ignore = true)
	void update(ProjectWeekly state, @MappingTarget ProjectWeeklyVO vo);
	
}
