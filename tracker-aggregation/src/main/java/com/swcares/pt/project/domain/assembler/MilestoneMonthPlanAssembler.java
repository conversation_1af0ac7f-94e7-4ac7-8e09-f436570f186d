/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPlanAssembler.java <br>
 * Package：com.swcares.pt.project.assembler <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月14日 下午4:08:59 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.assembler;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.LoginUser;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.domain.factory.MilestoneMonthPlanFactory;
import com.swcares.pt.project.dto.MilestoneMonthPlanDTO;
import com.swcares.pt.project.vo.MilestoneMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.base.assembler.MonthPlanAssembler <br>
 * Description：MilestoneMonthPlan域对象转换器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月14日 下午4:08:59 <br>
 * @version v1.0 <br>  
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
	nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MilestoneMonthPlanAssembler {
	
	MilestoneMonthPlanAssembler INSTANCE = Mappers.getMapper(MilestoneMonthPlanAssembler.class);

	/**
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "managementUnit", expression = "java(com.swcares.pt.base.assembler.converter.TeamConverter.convert(p.getManagementUnit()))")
	ProjectMonthPlanDTO toDTO(ProjectMonthPlanBO p);
	
	default List<ProjectMonthPlanDTO> toDTOs(List<ProjectMonthPlanBO> ds){
		if(ListUtils.isEmpty(ds)) {
			return ListUtils.newArrayList();
		}
		return ds.stream().map(MonthPlanAssembler.INSTANCE::toDTO).collect(Collectors.toList());
	}*/
	
	default MilestoneMonthPlan toDomain(MilestoneMonthPlanDTO dto, LoginUser user) {
		MilestoneMonthPlan state = MilestoneMonthPlanFactory.create();
		INSTANCE.update(dto, user, state);
		return state;
	}
	
	default List<MilestoneMonthPlan> toDomains(List<MilestoneMonthPlanDTO> states, LoginUser user){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<MilestoneMonthPlan> datas = ListUtils.newArrayList();
		states.forEach(s -> {
			datas.add(MilestoneMonthPlanAssembler.INSTANCE.toDomain(s, user));
		});
		return datas;
	}
	
	default MilestoneMonthPlan toDomain(MilestoneMonthPlanVO vo, LoginUser user) {
		MilestoneMonthPlan state = MilestoneMonthPlanFactory.create();
		INSTANCE.update(vo, user, state);
		return state;
	}
	
	default List<MilestoneMonthPlan> toDomains(LoginUser user, List<MilestoneMonthPlanVO> states){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<MilestoneMonthPlan> datas = ListUtils.newArrayList();
		states.forEach(s -> {
			datas.add(MilestoneMonthPlanAssembler.INSTANCE.toDomain(s, user));
		});
		return datas;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "weekPlans", ignore = true)
	@Mapping(target = "monthPlanCode", ignore = true)
	@Mapping(source = "dto.id", target = "id")
	@Mapping(source = "dto.jobNumber", target = "jobNumber")
	void update(MilestoneMonthPlanDTO dto, LoginUser user, @MappingTarget MilestoneMonthPlan state);
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "weekPlans", ignore = true)
	@Mapping(target = "monthPlanCode", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdTime", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedTime", ignore = true)
	@Mapping(source = "vo.id", target = "id")
	@Mapping(source = "vo.jobNumber", target = "jobNumber")
	void update(MilestoneMonthPlanVO vo, LoginUser user, @MappingTarget MilestoneMonthPlan state);
	
}
