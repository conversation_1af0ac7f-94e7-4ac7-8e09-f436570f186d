package com.swcares.pt.project.domain;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.project.repository.ProjectMonthPaceRepository;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectMonthPace <br>
 * Description：项目月进度 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectMonthPace extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 项目编码 */
    private String projectCode;
    
    /** 月计划编号 */
    private String monthPlanCode;

    /** 项目年 */
    private Integer projectYear;
    
    /** 所属经营单元 */
    private Long managementUnit;
    
    /** 所属交付组 */
    private Long deliveryTeam;
    
    /** 项目经理工号 */
    private String jobNumber;

    /** 统计月份 */
    private String statMonth;

    /** 统计日期 */
    private LocalDate statDate;
    
    /** 当月累计PV(人天) */
    private BigDecimal planWorkLoad;
    
    /** 当月累计EV(人天) */
    private BigDecimal workLoadTotal;

    /** 进度偏差 */
    private BigDecimal paceOffset;

    /** 里程碑滞后数量 */
    private Integer milestoneOverdue;

    /** 人工补充预警 */
    private Integer staffEarlyWarning;

    /** 进度风险 */
    private Integer paceRisk;

    private ProjectMonthPaceRepository repository;

	public ProjectMonthPace(ProjectMonthPaceRepository repository) {
		this.repository = repository;
	}
	
	public static ProjectMonthPace build(ProjectMonthPaceRepository repository) {
		return new ProjectMonthPace(repository);
	}

    public boolean save() {
    	return this.repository.save(this);
    }
    
    /**
     * Description：同步更新项目月度进展 <br>
     * author：罗江林 <br>
     * date：2025年3月26日 下午1:19:00 <br>
     * @param w
     * @param original <br>
     */
    public void synch(ProjectWeekly w, ProjectMonthPaceVO original) {
    	if(original != null) {
    		setId(original.getId());
		}
    	setPlanWorkLoad(w.getWeekTotalPv());
    	setWorkLoadTotal(w.getWeekTotalEv());
    	setStatDate(w.getEndDate());
    	setProjectYear(w.getYear());
    	setStatMonth(w.getWeekMonth());
		this.repository.save(this);
    }

}
