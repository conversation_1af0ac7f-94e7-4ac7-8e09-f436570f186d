package com.swcares.pt.project.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectWeeklyVO <br>
 * Description：项目周报VO <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectWeeklyVO", description="项目周报VO")
public class ProjectWeeklyDetailVO extends ProjectWeeklyVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "周报明细项")
    private List<WeeklyItemsVO> items;

}
