/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPlanRepository.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:48:07 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.dto.MilestoneMonthPlanDTO;
import com.swcares.pt.project.vo.MilestoneMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthPlanRepository <br>
 * Description：里程碑月计划仓储接口定义 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午1:48:07 <br>
 * @version v1.0 <br>  
 */
public interface MilestoneMonthPlanRepository extends BaseRepository<MilestoneMonthPlan, MilestoneMonthPlanVO>{
	
	/**
	 * Description：根据查询条件加载里程碑月计划数据集 <br>
	 * author：罗江林 <br>
	 * date：2024年8月15日 下午3:24:55 <br>
	 * @param params
	 * @return <br>
	 */
	List<MilestoneMonthPlanVO> getByCondition(MilestoneMonthPlanDTO params);
	
}
