/**  
 * All rights Reserved, Designed By <br>
 * Title：WeekPlanAssembler.java <br>
 * Package：com.swcares.pt.project.assembler <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:41:51 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.domain.assembler;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.common.model.LoginUser;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.domain.factory.ProjectWeekPlanFactory;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**   
 * ClassName：com.swcares.pt.project.assembler.WeekPlanAssembler <br>
 * Description：ProjectWeekPlan域对象转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:41:51 <br>
 * @version v1.0 <br>  
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WeekPlanAssembler {

	WeekPlanAssembler INSTANCE = Mappers.getMapper(WeekPlanAssembler.class);
	
	default ProjectWeekPlan toDomain(ProjectWeekPlanDTO dto, LoginUser user) {
		ProjectWeekPlan state = ProjectWeekPlanFactory.create();
		INSTANCE.update(dto, user, state);
		return state;
	}
	
	default List<ProjectWeekPlan> toDomains(List<ProjectWeekPlanDTO> states, LoginUser user){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<ProjectWeekPlan> datas = ListUtils.newArrayList();
		states.forEach(s -> {
			datas.add(WeekPlanAssembler.INSTANCE.toDomain(s, user));
		});
		return datas;
	}

	default ProjectWeekPlan toDomain(ProjectWeekPlanVO vo, LoginUser user) {
		ProjectWeekPlan state = ProjectWeekPlanFactory.create();
		INSTANCE.update(vo, user, state);
		return state;
	}
	
	default List<ProjectWeekPlan> toDomains(LoginUser user, List<ProjectWeekPlanVO> states){
		if(ListUtils.isEmpty(states)) {
			return ListUtils.newArrayList();
		}
		List<ProjectWeekPlan> datas = ListUtils.newArrayList();
		states.forEach(s -> {
			datas.add(WeekPlanAssembler.INSTANCE.toDomain(s, user));
		});
		return datas;
	}
	
	ProjectWeekPlanVO toVO(ProjectWeekPlan pi);
	/**
	ProjectWeekPlanDTO toDTO(ProjectWeekPlanBO pi);
	
	default List<ProjectWeekPlanDTO> toDTOs(List<ProjectWeekPlanBO> pis){
		if(ListUtils.isEmpty(pis)) {
			return ListUtils.newArrayList();
		}
		return pis.stream().map(WeekPlanAssembler.INSTANCE::toDTO).collect(Collectors.toList());
	}*/
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "deleted", ignore = true)
	@Mapping(source = "dto.id", target = "id")
	void update(ProjectWeekPlanDTO dto, LoginUser user, @MappingTarget ProjectWeekPlan state);
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "year", ignore = true)
	@Mapping(target = "deleted", ignore = true)
	@Mapping(source = "vo.id", target = "id")
	void update(ProjectWeekPlanVO vo, LoginUser user, @MappingTarget ProjectWeekPlan state);
	
}
