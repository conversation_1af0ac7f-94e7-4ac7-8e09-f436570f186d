package com.swcares.pt.project.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.baseframe.utils.collect.MapUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName：com.swcares.pt.project.pmt.entity.ProjectWeekPlanDTO <br>
 * Description：项目周计划 <br>
 * Copyright © 2025 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ProjectWeekPlanDTO", description="项目周计划DTO")
public class ProjectWeekPlanDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;
    
    /** 扩展查询条件参数集 */
    private Map<String, Object> params;

    @ApiModelProperty(value = "周计划编号")
    private String weekPlanCode;

    @ApiModelProperty(value = "月计划编号")
    private String monthPlanCode;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "里程碑编号")
    private String milestoneCode;
    
    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "年度第几周")
    private Integer yearWeekNum;

    @ApiModelProperty(value = "工作天数")
    private Integer workDays;

    @ApiModelProperty(value = "计划工量(PV-人天)")
    private BigDecimal planWorkLoad;
    
    @ApiModelProperty(value = "累计工量(PV-人天)")
    private BigDecimal accumulateWorkLoad;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;
    
    @Builder.Default
    private Integer weekNumGt = 0;
    
    public Map<String, Object> getParams(){
    	if(MapUtils.isEmpty(params)) {
    		params = MapUtils.newHashMap();
    	}
    	return params;
    }

}
