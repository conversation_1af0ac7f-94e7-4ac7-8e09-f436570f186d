package com.swcares.pt.project.domain.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.swcares.pt.project.domain.ProjectRiskTrack;
import com.swcares.pt.project.repository.ProjectRiskTrackRepository;

/**   
 * ClassName：com.swcares.pt.project.domain.factory.ProjectRiskTrackFactory <br>
 * Description：项目风险对象工厂类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午2:10:51 <br>
 * @version v1.0 <br>  
 */
@Component
public class ProjectRiskTrackFactory {

	private static ProjectRiskTrackRepository repository;
	
	@Autowired
	public ProjectRiskTrackFactory(ProjectRiskTrackRepository repository) {
		ProjectRiskTrackFactory.repository = repository;
	}
	
	public static ProjectRiskTrack create() {
		return ProjectRiskTrack.build(repository);
	}
	
}
