package com.swcares.pt.quality.repository;

import java.util.List;

import com.swcares.pt.core.base.BaseRepository;
import com.swcares.pt.quality.domain.QualityProductMeasure;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;

/**
 * ClassName：com.swcares.pt.quality.repository.QualityProductMeasureRepository <br>
 * Description：产品质量度量仓储接口 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
public interface QualityProductMeasureRepository extends BaseRepository<QualityProductMeasure, QualityProductMeasureVO> {

    /**
     * Description：根据条件查询产品质量度量列表 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param params 查询条件
     * @return 质量度量列表 <br>
     */
    List<QualityProductMeasureVO> getByCondition(QualityProductMeasureDTO params);

    /**
     * Description：根据产品编码查询最新的质量度量 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param productCode 产品编码
     * @return 最新的质量度量 <br>
     */
    QualityProductMeasureVO getLatestByProductCode(String productCode);

}
