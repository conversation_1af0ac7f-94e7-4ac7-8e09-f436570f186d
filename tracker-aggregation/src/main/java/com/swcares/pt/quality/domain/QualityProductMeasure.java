package com.swcares.pt.quality.domain;

import java.time.LocalDate;

import com.swcares.pt.common.model.BaseDomain;
import com.swcares.pt.quality.repository.QualityProductMeasureRepository;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.pt.quality.domain.QualityProductMeasure <br>
 * Description：产品质量度量领域实体 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityProductMeasure extends BaseDomain {

    private static final long serialVersionUID = 1L;

    /** 产品编码 */
    private String productCode;

    /** 统计月份 */
    private LocalDate statisticMonth;

    /** 统计时间 */
    private LocalDate statisticDate;

    /** sonar分析时间 */
    private LocalDate sonarDate;

    /** 安全评级 */
    private String securityRating;

    /** 安全问题数量 */
    private String securityIssues;

    /** 可靠性评级 */
    private String reliabilityRating;

    /** 可维护性问题数量 */
    private String reliabilityIssues;

    /** 新代码可维护性评级 */
    private String newMaintainabilityRating;

    /** 可维护性问题数量 */
    private String maintainabilityIssues;

    /** 重复代码行密度 */
    private String duplicatedLinesDensity;

    /** 代码覆盖率 */
    private String coverage;

    /** 非注释代码行数 */
    private String ncloc;

    /** 年代码增量 */
    private String annualIncrement;

    private QualityProductMeasureRepository repository;

    public QualityProductMeasure(QualityProductMeasureRepository repository) {
        this.repository = repository;
    }

    /**
     * Description：保存产品质量度量 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @return 是否成功 <br>
     */
    public boolean save() {
        return repository.save(this);
    }

    /**
     * Description：删除产品质量度量 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     */
    public void remove() {
        // 领域逻辑：标记删除
        // 可以在这里添加删除前的业务验证逻辑
    }

}
