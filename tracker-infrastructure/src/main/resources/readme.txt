基础设施层 infrastructure:
	应用的所有流量出口。包括：
		1. 资源库接口的实现
		2. 数据库操作接口、数据库实现（如果使用mybatis，则包含 resource/*.xml）、数据库对象 DO、DO 转化类
		3. 中间件的实现、文件系统实现、缓存实现、消息实现 等
		4. 第三方服务接口的实现

基础层：
	对所有上层提供技术能力，包括：数据操作，发送消息，消费消息，缓存，rpc实现(第三方服务)等
依赖：
	领域层（order-domain）
	应用层（order-application）

RPC(Remote Procedure Call) https://zhuanlan.zhihu.com/p/672747365
	RPC 的基本思想：本地计算机上的客户端程序调用远程服务器上的过程或子程序，就像调用本地过程一样，而不用关心底层网络通信的细节。

RPC 工作原理
	客户端调用：客户端通过本地调用远程过程的方式，就像调用本地方法一样
	数据序列化：客户端将调用的参数序列化为可以在网络上传输的格式，如二进制流或 JSON
	网络传输：序列化后的数据通过网络传输到远程服务器
	数据反序列化：服务器接收到数据后，将其反序列化为原始参数
	服务端执行：服务器执行相应的过程，并将结果返回
	结果序列化：服务器将执行结果序列化为可以在网络上传输的格式
	网络传输：序列化后的结果通过网络传输到客户端
	结果反序列化：客户端接收到结果后，将其反序列化为最终的返回值
	客户端接收结果：客户端获得最终的执行结果，完成整个过程
RPC 优点
	抽象性：提供了一种高层抽象，使开发者可以像调用本地方法一样调用远程服务，无需关心底层网络通信
	封装性：隐藏了底层通信细节，提供了类似于本地调用的封装，使得分布式系统开发更为简单
	效率：相对于其他通信方式，由于数据的二进制传输和更紧凑的协议，RPC 通常更加高效
	可扩展性：提供了一种扩展服务的机制，可以在不修改客户端代码的情况下添加新的服务或更新服务版本
	
RPC 适用场景
	分布式系统场景：当系统的各个模块分布在不同的服务器上时，RPC 提供了方便的远程调用机制，使得模块间的通信更加简便
	微服务架构场景：在微服务架构中，各个微服务可能运行在不同的进程或主机上，RPC 提供了一种有效的通信方式，支持微服务之间的远程调用
	性能要求高场景：RPC 框架通常采用二进制协议和高效的序列化方式，因此在性能要求较高的场景中表现比较优越
	多语言支持场景：RPC 框架通常支持多种编程语言，使得不同语言编写的模块可以方便地进行远程调用
	服务治理场景：RPC 框架通常提供服务注册、负载均衡、服务发现等功能，有助于构建健壮的分布式系统
	接口规范明确场景：RPC 框架通常使用 IDL 或者其他接口描述语言，接口规范明确，便于团队协作和版本管理
	总之，RPC 在需要高效、可靠的远程调用和分布式系统通信的场景中具有较强的适用性。	
	
	
	
	
	
	
