<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.vo.ProjectInfoVO">
        <result column="id" 					property="id" 				/>
        <result column="project_code" 			property="projectCode" 		/>
        <result column="project_sn" 			property="projectSn" 		/>
        <result column="project_name" 			property="projectName" 		/>
        <result column="project_alias" 			property="projectAlias" 	/>
        <result column="project_nature" 		property="projectNature" 	/>
        <result column="customer_name" 			property="customerName" 	/>
        <result column="management_unit" 		property="managementUnit" 	/>
        <result column="delivery_team" 			property="deliveryTeam" 	/>
        <result column="priority_level" 		property="priorityLevel" 	/>
        <result column="job_number" 			property="jobNumber" 		/>
        <result column="project_manager" 		property="projectManager" 	/>
        <result column="project_stage" 			property="projectStage" 	/>
        <result column="project_state" 			property="projectState" 	/>
        <result column="approval_date" 			property="approvalDate" 	/>
        <result column="plan_start_date" 		property="planStartDate" 	/>
        <result column="plan_end_date" 			property="planEndDate" 		/>
        <result column="plan_closing_date" 		property="planClosingDate" 	/>
        <result column="budget_project" 		property="budgetProject" 	/>
        <result column="pretax_income" 			property="pretaxIncome"  />
        <result column="revenue" 				property="revenue" 			/>
        <result column="remark" 		property="remark" 		/>
        <result column="created_by" 	property="createdBy" 	/>
        <result column="created_time" 	property="createdTime" 	/>
        <result column="updated_by"	 	property="updatedBy" 	/>
        <result column="updated_time" 	property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectInfoVO">
        SELECT  t.id, 
				t.project_code, 
				t.project_name, 
				t.project_alias, 
				t.project_nature, 
				t.customer_name, 
				t.management_unit, 
				t.delivery_team, 
				t.priority_level, 
				t.job_number, 
				t.project_manager, 
				t.project_stage, 
				t.project_state, 
				t.approval_date, 
				t.plan_start_date, 
				t.plan_end_date, 
				t.plan_closing_date, 
				t.budget_project, 
				t.pretax_income, 
				t.revenue, 
				t.remark, 
				(SELECT ROUND(SUM(k.year_plan_cost) / 10000, 3) as plan_cost_total 
					FROM pmt_project_year_stat k 
					WHERE k.deleted = 0 AND k.project_code = t.project_code 
					GROUP BY k.project_code
				) AS plan_cost_total,
				ROUND((y.year_plan_cost / 10000), 3) as year_plan_cost
		FROM pmt_project_info t 
			LEFT JOIN pmt_project_year_stat y ON y.project_code = t.project_code AND y.deleted = 0 AND y.project_year = #{p.projectYear}
        <where>
        	t.deleted = 0 
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">
        		AND (t.project_code like concat('%', #{p.projectCode}, '%') OR t.project_name like concat('%', #{p.projectCode}, '%'))
        	</if>
        	<if test="p.projectName != null and p.projectName != ''">AND t.project_name like concat('%', #{p.projectName}, '%')</if>
        	<if test="p.projectAlias != null and p.projectAlias != ''">AND t.project_alias like concat('%', #{p.projectAlias}, '%')</if>
        	<if test="p.projectNature != null">AND t.project_nature = #{p.projectNature}</if>
        	<if test="p.projectStage != null">AND t.project_stage = #{p.projectStage}</if>
        	<if test="p.projectState != null">AND t.project_state = #{p.projectState}</if>
        	<if test="p.priorityLevel != null">AND t.priority_level = #{p.priorityLevel}</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.budgetProject != null">AND t.budget_project = #{p.budgetProject}</if>
        	${p.params.dataScope}
        </where>
        ORDER BY t.approval_date desc
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectInfoVO">
        SELECT t.* FROM pmt_project_info t
        <where>
        	t.deleted = 0 
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">AND t.project_code = #{p.projectCode}</if>
        	<if test="p.projectSn != null and p.projectSn != ''">AND t.project_sn = #{p.projectSn}</if>
        	<if test="p.projectName != null and p.projectName != ''">AND t.project_sn like concat('%', #{p.projectName}, '%')</if>
        	<if test="p.projectAlias != null and p.projectAlias != ''">AND t.project_alias like concat('%', #{p.projectAlias}, '%')</if>
        	<if test="p.projectNature != null">AND t.project_nature = #{p.projectNature}</if>
        	<if test="p.projectStage != null">AND t.project_stage = #{p.projectStage}</if>
        	<if test="p.projectState != null">AND t.project_state = #{p.projectState}</if>
        	<if test="p.priorityLevel != null">AND t.priority_level = #{p.priorityLevel}</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.budgetProject != null">AND t.budget_project = #{p.budgetProject}</if>
        	<if test="p.params.pcodes != null and p.params.pcodes.size() > 0" >
        		AND t.project_code IN
        		<foreach collection="p.params.pcodes" item="pcode" open="(" separator="," close=")">
		 			#{pcode}
		        </foreach>
        	</if>
        </where>
    </select>
    
    <select id="priorityStat" resultType="com.swcares.pt.project.vo.ProjectPriorityStatVO">
    	SELECT 
			COUNT(1) total_quantity,
			SUM(CASE WHEN t.priority_level = 1 THEN 1 ELSE 0 END) one_level, 
			SUM(CASE WHEN t.priority_level = 2 THEN 1 ELSE 0 END) two_level,
			SUM(CASE WHEN t.priority_level = 3 THEN 1 ELSE 0 END) three_level, 
			SUM(CASE WHEN t.priority_level = 4 THEN 1 ELSE 0 END) four_level
		FROM pmt_project_info t 
		<where>
			t.deleted = 0
			<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
		</where>
    </select>
    
    <select id="projectOverviewStat" resultType="com.swcares.pt.project.vo.ProjectOverviewVO">
    	SELECT 
    		t.project_year as stat_year,
			(SELECT COUNT(1) AS total FROM pmt_project_info t WHERE t.project_year = #{p.projectYear}) AS total_quantity,
			ROUND((SUM(IFNULL(t.plan_month_work_load, 0)) * 21 / 12), 3) AS plan_work_load,
			ROUND((SUM(IFNULL(t.plan_month_coding_produce, 0)) * 21 / 12), 3) AS coding_work_cost
		FROM pmt_project_month_plan t 
		<where>
			t.deleted = 0
			<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
		</where>
		GROUP BY t.project_year
    </select>
    
    <select id="projectStageStat" resultType="com.swcares.pt.project.vo.ProjectStageStatVO">
    	SELECT 
			t.project_stage,
			COUNT(1) AS project_count
		FROM pmt_project_info t
		<where>
			t.deleted = 0
			<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
		</where>
		GROUP BY t.project_stage
    </select>
    
    <select id="selectProjectRiskStat" resultType="com.swcares.pt.project.vo.ManagementUnitRiskStatVO">
    	SELECT 
			t.management_unit, 
			(SELECT d.dict_label FROM sys_dictionary_data d WHERE d.dict_type = 'plm_teams' AND d.dict_value = t.management_unit) AS management_name,
			DATE_FORMAT(CONCAT(#{p.statMonth},'-01'), '%m') AS stat_month,
			COUNT(t.project_code) AS project_count,
			SUM(CASE WHEN t.pace_risk = 1 THEN 1 ELSE 0 END) AS pace_risk_count,
			SUM(CASE WHEN t.pace_risk = 1 THEN 1 ELSE 0 END) AS cost_risk_count,
			IFNULL((SELECT 
					ROUND(SUM(t1.plan_coding_work_load - t1.finish_coding_work_load), 3) AS pace_rate
				FROM pmt_project_month_pace t1 
					WHERE t1.stat_month = #{p.statMonth} AND t1.management_unit = t.management_unit 
			), 0) AS produce_gap,
			IFNULL((
				SELECT    
					ROUND(IFNULL(t2.finish_coding_work_load / t1.finish_coding_work_cost, 0), 3) AS pace_rate
				FROM(
					SELECT 
						t.management_unit,
						ROUND(SUM(t.finish_coding_work_cost), 0) AS finish_coding_work_cost
					FROM pmt_project_month_cost t 
						WHERE t.stat_month  = #{p.statMonth}
						GROUP BY t.management_unit
				) t1 LEFT JOIN (
					SELECT 
						t.management_unit,
						ROUND(SUM(t.finish_coding_work_load), 0) AS finish_coding_work_load
					FROM pmt_project_month_pace t
						WHERE t.stat_month = #{p.statMonth}
						GROUP BY t.management_unit
				) t2 on t1.management_unit = t2.management_unit WHERE t1.management_unit = t.management_unit
			), 0) * 100 AS cost_rate
		FROM pmt_project_info t 
			WHERE t.stat_date = #{p.statMonth}
			GROUP BY t.management_unit
    </select>
    
</mapper>
