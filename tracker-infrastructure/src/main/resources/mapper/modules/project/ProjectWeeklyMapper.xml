<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectWeeklyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.vo.ProjectWeeklyDetailVO">
        <result column="id" 				property="id" />
        <result column="project_code" 		property="projectCode" />
        <result column="project_name" 		property="projectName" />
        <result column="month_plan_code" 	property="monthPlanCode" />
        <result column="week_plan_code" 	property="weekPlanCode" />
        <result column="week_month" 		property="weekMonth" />
        <result column="year" 				property="year" />
        <result column="year_week_num" 		property="yearWeekNum" />
        <result column="management_unit" 	property="managementUnit" />
        <result column="delivery_team" 		property="deliveryTeam" />
        <result column="job_number"		 	property="jobNumber" />
        <result column="project_manager"  	property="projectManager" 	/>
        <result column="current_week_ev" 	property="currentWeekEv" />
        <result column="week_total_ev" 		property="weekTotalEv" />
        <result column="week_total_pv" 		property="weekTotalPv" />
        <result column="last_week_total_ev" property="lastWeekTotalEv" />
        <result column="pace_offset" 		property="paceOffset" />
        <result column="fillIn_by" 			property="fillInBy" />
        <result column="fillIn_time" 		property="fillInTime" />
        <result column="pace_risk" 			property="paceRisk" />
        <result column="risk_num" 			property="riskNum" />
        <result column="start_date" 		property="startDate" />
        <result column="end_date" 			property="endDate" />
        <result column="weekly_state" 		property="weeklyState" />
        <result column="description" 		property="description" />
        <result column="remark" 			property="remark" />
        <result column="created_by" 		property="createdBy" />
        <result column="created_time" 		property="createdTime" />
        <result column="updated_by" 		property="updatedBy" />
        <result column="updated_time" 		property="updatedTime" />
        <collection property="items" ofType="com.swcares.pt.project.vo.WeeklyItemsVO">
        	<result column="item_id" 				property="id" />
	        <result column="weekly_id" 				property="weeklyId" />
	        <result column="item_project_code" 		property="projectCode" />
	        <result column="item_milestone_code" 	property="milestoneCode" />
	        <result column="milestone_name" 		property="milestoneName" />
	        <result column="item_start_date" 		property="startDate" />
	        <result column="item_end_date" 			property="endDate" />
	        <result column="plan_work_load" 		property="planWorkLoad" />
	        <result column="item_current_week_ev" 		property="currentWeekEv" />
	        <result column="item_last_week_total_ev" 	property="lastWeekTotalEv" />
	        <result column="item_week_total_ev" 		property="weekTotalEv" />
	        <result column="item_week_total_pv" 		property="weekTotalPv" />
	        <result column="item_pace_offset" 			property="paceOffset" />
	        <result column="item_pace_risk" 			property="paceRisk" />
        </collection>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectWeeklyVO">
        SELECT  t.id, 
				t.project_code, 
				t.month_plan_code, 
				t.week_plan_code, 
				t.`year`, 
				t.week_month, 
				t.year_week_num, 
				t.work_days, 
				t.management_unit, 
				t.delivery_team, 
				t.job_number, 
				t.project_manager, 
				t.week_total_ev, 
				t.week_total_pv, 
				t.current_week_ev, 
				t.last_week_total_ev, 
				t.pace_offset, 
				t.fill_in_by, 
				t.fill_in_time, 
				t.pace_risk, 
				t.start_date, 
				t.end_date, 
				t.weekly_state, 
				t.description, 
				t.remark, 
				t.created_by, 
				t.created_time, 
				t.updated_by, 
				t.updated_time,
        		p.project_name,
        		p.project_alias,
        		(SELECT count(1) FROM pmt_project_risk_track r 
        			WHERE r.project_code = t.project_code AND r.risk_state = 0
        			GROUP BY r.project_code) AS risk_num
        	FROM pmt_project_weekly t 
        		LEFT JOIN pmt_project_info p ON t.project_code = p.project_code AND p.deleted = 0
        <where>
        	t.deleted = 0 
        	<if test="p.monthPlanCode != null and p.monthPlanCode != ''">AND t.month_plan_code = #{p.monthPlanCode}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">
        		AND (t.project_code like concat('%', #{p.projectCode}, '%') OR p.project_name like concat('%', #{p.projectCode}, '%'))
        	</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.projectManager != null and p.projectManager != ''">AND t.project_manager = #{p.projectManager}</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.year != null">AND t.year = #{p.year}</if>
        	<if test="p.yearWeekNum != null">AND t.year_week_num = #{p.yearWeekNum}</if>
        	<if test="p.startDate != null">
        		AND t.start_date &lt;= #{p.startDate} AND t.end_date &gt;= #{p.startDate}
        	</if>
        	${p.params.dataScope}
        </where>
        ORDER BY t.weekly_state ASC, t.created_time DESC
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectWeeklyVO">
        SELECT t.* FROM pmt_project_weekly t
        <where>
        	t.deleted = 0 
        	<if test="p.monthPlanCode != null and p.monthPlanCode != ''">AND t.month_plan_code = #{p.monthPlanCode}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">AND t.project_code = #{p.projectCode}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.year != null">AND t.year = #{p.year}</if>
        	<if test="p.yearWeekNum != null">AND t.year_week_num = #{p.yearWeekNum}</if>
        </where>
    </select> 
    
    <select id="selectJoinWeeklyByCondition" resultMap="BaseResultMap">
        SELECT  t.*,
        		(SELECT count(1) FROM pmt_project_risk_track r 
        			WHERE r.project_code = t.project_code AND r.risk_state = 0
        			GROUP BY r.project_code) AS risk_num, 
        		p.project_name,
        		w.id as item_id,
        		w.weekly_id, 
        		w.project_code as item_project_code,
        		w.milestone_code as item_milestone_code, 
				w.current_week_ev as item_current_week_ev, 
				w.last_week_total_ev as item_last_week_total_ev, 
				w.week_total_ev as item_week_total_ev, 
				w.week_total_pv as item_week_total_pv,
				w.pace_offset as item_pace_offset,
				w.pace_risk as item_pace_risk,
        		m.milestone_name,
        		m.start_date as item_start_date,
        		m.end_date as item_end_date,
        		m.plan_work_load  
        	FROM pmt_project_weekly t 
        		LEFT JOIN pmt_weekly_items w ON w.weekly_id = t.id AND w.deleted = 0
        		LEFT JOIN pmt_project_milestone m ON m.milestone_code = w.milestone_code AND m.deleted = 0
        		LEFT JOIN pmt_project_info p ON t.project_code = p.project_code AND p.deleted = 0
        <where>
        	t.deleted = 0 
        	<if test="p.id != null">AND t.id = #{p.id}</if>
        	<if test="p.monthPlanCode != null and p.monthPlanCode != ''">AND t.month_plan_code = #{p.monthPlanCode}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">AND t.project_code = #{p.projectCode}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.year != null">AND t.year = #{p.year}</if>
        	<if test="p.yearWeekNum != null">AND t.year_week_num = #{p.yearWeekNum}</if>
        	<if test="p.weeklyState != null">AND t.weekly_state = #{p.weeklyState}</if>
        	<if test="p.milestoneCode != null and p.milestoneCode != ''">AND w.milestone_code = #{p.milestoneCode}</if>
        	<if test="p.params.states != null and p.params.states.size() > 0">
        		AND t.weekly_state IN
        		<foreach collection="p.params.states" item="state" open="(" separator="," close=")">
        			#{state}
        		</foreach>
        	</if>
        </where>
    </select>
    
</mapper>
