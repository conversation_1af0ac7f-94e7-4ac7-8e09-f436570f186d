<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectMilestoneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.vo.ProjectMilestoneVO">
        <result column="id" 				property="id" />
        <result column="milestone_code" 	property="milestoneCode" />
        <result column="milestone_name" 	property="milestoneName" />
        <result column="project_code" 		property="projectCode" />
        <result column="management_unit" 	property="managementUnit" />
        <result column="job_number" 		property="jobNumber" />
        <result column="delivery_team" 		property="deliveryTeam" />
        <result column="project_year" 		property="projectYear" />
        <result column="start_date" 		property="startDate" />
        <result column="end_date" 			property="endDate" />
        <result column="work_day" 			property="workDay" />
        <result column="plan_work_load" 	property="planWorkLoad" />
        <result column="remark" 			property="remark" />
        <result column="created_by" 		property="createdBy" />
        <result column="created_time" 		property="createdTime" />
        <result column="updated_by" 		property="updatedBy" />
        <result column="updated_time" 		property="updatedTime" />
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectMilestoneVO">
        SELECT  t.*,
        		p.project_name,
        		p.project_alias
        	FROM pmt_project_milestone t 
        	LEFT JOIN pmt_project_info p ON p.project_code = t.project_code AND p.deleted = 0
        <where>
        	t.deleted = 0
        	<if test="p.projectCode != null and p.projectCode != ''"> 
        		AND (t.project_code like concat('%', #{p.projectCode}, '%') 
        			OR t.milestone_code like concat('%', #{p.projectCode}, '%')
        			OR p.project_name like concat('%', #{p.projectCode}, '%')
        		)
        	</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.projectYear != null">AND t.project_year = #{p.projectYear}</if>
        	${p.params.dataScope}
        </where>
        ORDER BY t.created_time desc
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectMilestoneVO">
		SELECT * FROM pmt_project_milestone t 
		<where>
        	t.deleted = 0
        	<if test="p.projectCode != null and p.projectCode != ''"> AND t.project_code = #{p.projectCode} </if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        </where>
    </select>
    
    <select id="selectByProjectState" resultType="com.swcares.pt.project.vo.ProjectMilestoneVO">
		SELECT t.* FROM pmt_project_milestone t 
			LEFT JOIN pmt_project_info p ON p.project_code = t.project_code
		<where>
        	t.deleted = 0 AND p.project_state = 1
        	<if test="p.projectCode != null and p.projectCode != ''"> AND t.project_code = #{p.projectCode} </if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        </where>
    </select>
    
    <update id="deleteByCondition">
    	UPDATE pmt_project_milestone t SET t.deleted = 1 
    	<where>
    		<if test="p.milestoneCode != null">AND t.milestone_code = #{p.milestoneCode}</if>
    		<if test="p.projectCode != null">AND t.project_code = #{p.projectCode}</if>
    	</where>
    </update>
    
</mapper>
