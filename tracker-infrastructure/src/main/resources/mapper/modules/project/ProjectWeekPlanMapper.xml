<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectWeekPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.entity.ProjectWeekPlanDO">
        <result column="id" 					property="id" />
        <result column="week_plan_code" 		property="weekPlanCode" />
        <result column="month_plan_code" 		property="monthPlanCode" />
        <result column="project_code" 			property="projectCode" />
        <result column="milestone_code" 		property="milestoneCode" />
        <result column="year" 					property="year" />
        <result column="year_week_num" 			property="yearWeekNum" />
        <result column="work_days" 				property="workDays" />
        <result column="plan_work_load" 		property="planWorkLoad" />
        <result column="accumulate_work_load" 	property="accumulateWorkLoad" />
        <result column="start_date" 			property="startDate" />
        <result column="end_date" 				property="endDate" />
        <result column="created_by" 			property="createdBy" />
        <result column="created_time" 			property="createdTime" />
        <result column="updated_by" 			property="updatedBy" />
        <result column="updated_time" 			property="updatedTime" />
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectWeekPlanVO">
        SELECT * FROM pmt_project_week_plan
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectWeekPlanVO">
        SELECT t.* FROM pmt_project_week_plan t
        <where>
        	t.deleted = 0 
        	<if test="p.monthPlanCode != null and p.monthPlanCode != ''">AND t.month_plan_code = #{p.monthPlanCode}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">AND t.project_code = #{p.projectCode}</if>
        	<if test="p.milestoneCode != null and p.milestoneCode != ''">AND t.milestone_code = #{p.milestoneCode}</if>
        	<if test="p.year != null">AND t.year = #{p.year}</if>
        	<if test="p.yearWeekNum != null"> AND t.year_week_num = #{p.yearWeekNum} </if>
        	<if test="p.startDate != null">
        		<choose>
        			<when test="p.weekNumGt != null and p.weekNumGt > 0">
        				AND t.start_date &lt;= #{p.startDate}
        			</when>
        			<otherwise>
		        		AND t.start_date = #{p.startDate}
        			</otherwise>
        		</choose>
        	</if>
        	<if test="p.params.pcodes != null and p.params.pcodes.size() > 0">
        		AND project_code IN
        		<foreach collection="p.params.pcodes" item="pcode" open="(" separator="," close=")">
		 			#{pcode}
		        </foreach> 
        	</if>
        </where>
    </select>
    
    <update id="deleteByCondition">
    	UPDATE pmt_project_week_plan t SET t.deleted = 1 
    	<where>
    		<if test="p.mCodes != null and p.mCodes.size() > 0 ">
    			AND t.milestone_code IN 
    			<foreach collection="p.mCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    		<if test="p.mpCodes != null and p.mpCodes != ''">
    			AND t.month_plan_code IN 
    			<foreach collection="p.mpCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    		<if test="p.pCodes != null and p.pCodes != ''">
    			AND t.project_code IN 
    			<foreach collection="p.pCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    	</where>
    </update>
    
    <delete id="removeByCondition">
    	DELETE FROM pmt_project_week_plan t 
    	<where>
    		<if test="p.mCodes != null and p.mCodes.size() > 0 ">
    			AND t.milestone_code IN 
    			<foreach collection="p.mCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    		<if test="p.mpCodes != null and p.mpCodes != ''">
    			AND t.month_plan_code IN 
    			<foreach collection="p.mpCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    		<if test="p.pCodes != null and p.pCodes != ''">
    			AND t.project_code IN 
    			<foreach collection="p.pCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    	</where>
    </delete>
    
</mapper>
