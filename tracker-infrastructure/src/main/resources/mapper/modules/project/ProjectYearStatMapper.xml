<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectYearStatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.entity.ProjectYearStatDO">
        <result column="id" 					property="id" />
        <result column="project_code" 			property="projectCode" />
        <result column="project_year" 			property="projectYear" />
        <result column="plan_work_load" 		property="planWorkLoad" />
        <result column="year_plan_cost" 		property="yearPlanCost" />
        <result column="year_cost_price" 		property="yearCostPrice" />
        <result column="remark" 				property="remark" />
        <result column="created_by" 			property="createdBy" />
        <result column="created_time" 			property="createdTime" />
        <result column="updated_by" 			property="updatedBy" />
        <result column="updated_time" 			property="updatedTime" />
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectYearStatVO">
        SELECT * FROM pmt_project_year_stat
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectYearStatVO">
        SELECT t.* FROM pmt_project_year_stat t
        <where>
        	t.deleted = 0 
        	<if test="p.projectCode != null and p.projectCode != ''"> AND t.project_code = #{p.projectCode} </if>
        	<if test="p.projectYear != null">AND t.project_year = #{p.projectYear}</if>
        </where>
    </select>
    
</mapper>
