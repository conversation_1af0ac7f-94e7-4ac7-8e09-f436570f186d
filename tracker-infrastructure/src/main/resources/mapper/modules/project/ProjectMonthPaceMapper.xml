<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectMonthPaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.vo.ProjectMonthPaceVO">
        <result column="id" 					property="id" 			/>
        <result column="project_code" 			property="projectCode" 	/>
        <result column="month_plan_code" 		property="monthPlanCode" />
        <result column="project_year" 			property="projectYear" 	/>
        <result column="stat_month" 			property="statMonth" 	/>
        <result column="stat_date" 				property="statDate" 	/>
        <result column="management_unit" 		property="managementUnit" />
        <result column="job_number" 			property="jobNumber" />
        <result column="delivery_team" 			property="deliveryTeam" />
        <result column="plan_work_load" 		property="planWorkLoad" />
        <result column="work_load_total" 		property="workLoadTotal" />
        <result column="pace_offset" 			property="paceOffset" 	/>
        <result column="milestone_overdue" 		property="milestoneOverdue" />
        <result column="staff_early_warning" 	property="staffEarlyWarning" />
        <result column="pace_risk" 				property="paceRisk" 	/>
        <result column="created_by" 			property="createdBy" 	/>
        <result column="created_time" 			property="createdTime" 	/>
        <result column="updated_by" 			property="updatedBy" 	/>
        <result column="updated_time" 			property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectMonthPaceVO">
        SELECT  t.id, 
				t.project_id, 
				t.project_code, 
				t.month_plan_code, 
				t.project_year, 
				t.management_unit, 
				t.job_number, 
				t.delivery_team, 
				t.stat_month,
				t.stat_date, 
				t.plan_work_load, 
				t.work_load_total,
				t.pace_offset * 100 as pace_offset, 
				t.milestone_overdue, 
				t.staff_early_warning, 
				t.pace_risk, 
				t.updated_by, 
				t.updated_time,
        		p.project_name,
        		p.project_alias
        	FROM pmt_project_month_pace t 
        		LEFT JOIN pmt_project_info p ON t.project_code = p.project_code AND p.deleted = 0
        <where>
        	t.deleted = 0 
        	<if test="p.projectYear != null and p.projectYear != ''">AND t.project_year = #{p.projectYear}</if>
        	<if test="p.statMonth != null and p.statMonth != ''">AND t.stat_month = #{p.statMonth}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">
        		AND (t.project_code like concat('%', #{p.projectCode}, '%') 
        			OR p.project_name like concat('%', #{p.projectCode}, '%')
        		)
        	</if>
        	<if test="p.projectName != null and p.projectName != ''">AND p.project_name like concat('%', #{p.projectName}, '%')</if>
        	<if test="p.projectAlias != null and p.projectAlias != ''">AND p.project_alias like concat('%', #{p.projectAlias}, '%')</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.statDate != null">AND t.stat_date = #{p.statDate}</if>
        	${p.params.dataScope}
        </where>
         ORDER BY t.project_code ASC, t.stat_month DESC
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectMonthPaceVO">
        select t.* from pmt_project_month_pace t
        <where>
        	t.deleted = 0 
        	<if test="p.projectYear != null and p.projectYear != ''">AND t.project_year = #{p.projectYear}</if>
        	<if test="p.statMonth != null and p.statMonth != ''">AND t.stat_month = #{p.statMonth}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">AND t.project_code = #{p.projectCode}</if>
        	<if test="p.deliveryTeam != null">AND t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">AND t.job_number = #{p.jobNumber}</if>
        	<if test="p.managementUnit != null">AND t.management_unit = #{p.managementUnit}</if>
        	<if test="p.statDate != null">AND t.stat_date = #{p.statDate}</if>
        </where>
    </select>
    
    <select id="selectPaceAnalysis" resultType="com.swcares.pt.project.vo.ProjectAnalysisStatVO">
    	SELECT t1.*, t2.finish_coding_work_load FROM (
    		SELECT 
				t.stat_month,
				SUM(t.plan_coding_work_load) AS plan_coding_work_load
			FROM pmt_project_month_plan t 
				WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
				GROUP BY t.stat_month 
			) t1 LEFT JOIN (
			SELECT 
				t.stat_month,  
				SUM(t.finish_coding_work_load) AS finish_coding_work_load
			FROM pmt_project_month_pace t 
				WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
				GROUP BY t.stat_month 
		) t2 ON t1.stat_month = t2.stat_month ORDER BY t1.stat_month
    </select>
    
    <select id="selectMgtUnitPaceStat" resultType="com.swcares.pt.project.vo.ManagementUnitMonthStatVO">
    	SELECT 
			t.management_unit,
			(SELECT d.dict_label FROM sys_dictionary_data d WHERE d.dict_type = 'plm_teams' AND d.dict_value = t.management_unit) AS management_name,
			t.stat_month,
			ROUND(SUM(t.plan_coding_work_load), 3) AS plan_coding_work_load,
			ROUND(SUM(t.plan_coding_work_load - t.finish_coding_work_load), 3) AS actual_finish,
			ROUND(SUM(t.finish_coding_work_load)/SUM(t.plan_coding_work_load), 3) * 100 AS pace_rate
		FROM pmt_project_month_pace t 
			WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
			GROUP BY t.management_unit, t.stat_month
    </select>
    
    
</mapper>
