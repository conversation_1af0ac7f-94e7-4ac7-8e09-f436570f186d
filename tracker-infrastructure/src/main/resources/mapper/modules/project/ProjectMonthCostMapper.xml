<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectMonthCostMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.entity.ProjectMonthCostDO">
        <result column="id" 			property="id" 			/>
        <result column="project_code" 	property="projectCode" 	/>
        <result column="project_year" 	property="projectYear" 	/>
        <result column="stat_month" 	property="statMonth" 	/>
        <result column="stat_date" 		property="statDate" 	/>
        <result column="plan_work_load" property="planWorkLoad" />
        <result column="management_unit" 			property="managementUnit" 			/>
        <result column="plan_month_coding_produce" 	property="planMonthCodingProduce" 	/>
        <result column="plan_coding_work_load" 		property="planCodingWorkLoad" 		/>
        <result column="month_coding_work_cost" 	property="monthCodingWorkCost" 		/>
        <result column="finish_coding_work_cost" 	property="finishCodingWorkCost" 	/>
        <result column="cost_offset" 				property="costOffset" 				/>
        <result column="staff_early_warning" 		property="staffEarlyWarning" 		/>
        <result column="cost_risk" 		property="costRisk" 	/>
        <result column="created_by" 	property="createdBy" 	/>
        <result column="created_time" 	property="createdTime" 	/>
        <result column="updated_by" 	property="updatedBy" 	/>
        <result column="updated_time" 	property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectMonthCostVO">
        select  t.*, 
        		p.project_name,
        		p.project_alias
        	from pmt_project_month_cost t
        		left join pmt_project_info p on t.project_code = p.project_code and t.project_year = p.project_year
        <where>
        	t.deleted = 0 
        	<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
        	<if test="p.statMonth != null and p.statMonth != ''">and t.stat_month = #{p.statMonth}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code like concat('%', #{p.projectCode}, '%')</if>
        	<if test="p.projectName != null and p.projectName != ''">and p.project_name like concat('%', #{p.projectName}, '%')</if>
        	<if test="p.projectAlias != null and p.projectAlias != ''">and p.project_alias like concat('%', #{p.projectAlias}, '%')</if>
        	<if test="p.managementUnit != null">and t.management_unit = #{p.managementUnit}</if>
        	<if test="p.statDate != null">and t.stat_date = #{p.statDate}</if>
        </where>
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectMonthCostVO">
        select t.* from pmt_project_month_cost t
        <where>
        	t.deleted = 0 
        	<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
        	<if test="p.statMonth != null and p.statMonth != ''">and t.stat_month = #{p.statMonth}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code = #{p.projectCode}</if>
        	<if test="p.statDate != null">and t.stat_date = #{p.statDate}</if>
        </where>
    </select>
    
    <select id="selectCostAnalysis" resultType="com.swcares.pt.project.vo.ProjectAnalysisStatVO">
    	SELECT t1.*, t2.finish_coding_work_load FROM (
	    		SELECT 
					t.stat_month,
					SUM(t.plan_coding_work_load) AS plan_coding_work_load
				FROM pmt_project_month_plan t 
					WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
					GROUP BY t.stat_month  
			) t1 LEFT JOIN (
				SELECT 
					t.stat_month,  
					SUM(t.finish_coding_work_cost) AS finish_coding_work_load
				FROM pmt_project_month_cost t 
					WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
					GROUP BY t.stat_month 
		) t2 ON t1.stat_month = t2.stat_month ORDER BY t1.stat_month
    </select>
    
    <select id="selectMgtUnitCostStat" resultType="com.swcares.pt.project.vo.ManagementUnitMonthStatVO">
    	SELECT  
			t1.*,
			(SELECT d.dict_label FROM sys_dictionary_data d WHERE d.dict_type = 'plm_teams' AND d.dict_value = t1.management_unit) AS management_name,
			t2.finish_coding_work_load,
			ROUND(IFNULL(t2.finish_coding_work_load/t1.finish_coding_work_cost, 0), 3) * 100 AS pace_rate,
			t1.finish_coding_work_cost - t1.plan_coding_work_load AS actual_finish
		FROM(
			SELECT 
				t.management_unit,
				t.stat_month,
				<!-- 计划编码工量 -->
				ROUND(SUM(t.plan_coding_work_load), 3) AS plan_coding_work_load,
				<!-- 编码使用工量 -->
				ROUND(SUM(t.finish_coding_work_cost), 3) AS finish_coding_work_cost
			FROM pmt_project_month_cost t 
				WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
				GROUP BY t.management_unit, t.stat_month
		) t1 LEFT JOIN (
			SELECT 
				t.management_unit,
				t.stat_month,
				<!-- 完成编码工量 -->
				ROUND(SUM(t.finish_coding_work_load), 3) AS finish_coding_work_load
			FROM pmt_project_month_pace t
				WHERE t.stat_month BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
				GROUP BY t.management_unit, t.stat_month
		) t2 on t1.management_unit = t2.management_unit AND t1.stat_month = t2.stat_month
    </select>
    
</mapper>
