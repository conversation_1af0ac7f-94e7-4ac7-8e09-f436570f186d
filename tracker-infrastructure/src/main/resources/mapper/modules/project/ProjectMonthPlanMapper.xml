<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectMonthPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.vo.ProjectMonthPlanVO">
        <result column="id" 						property="id" 			/>
        <result column="plan_code" 					property="planCode" 	/>
        <result column="project_code" 				property="projectCode" 	/>
        <result column="plan_month" 				property="planMonth" 	/>
        <result column="management_unit" 			property="managementUnit" />
        <result column="job_number" 				property="jobNumber"	/>
        <result column="delivery_team" 				property="deliveryTeam"	/>
        <result column="work_day" 					property="workDay"	/>
        <result column="start_date" 				property="startDate" 	/>
        <result column="end_date" 					property="endDate" 	/>
        <result column="plan_work_load" 			property="planWorkLoad" />
        <result column="plan_month_work_load" 		property="planMonthWorkLoad" />
        <result column="created_by" 				property="createdBy" 	/>
        <result column="created_time" 				property="createdTime" 	/>
        <result column="updated_by" 				property="updatedBy" 	/>
        <result column="updated_time" 				property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectMonthPlanVO">
        SELECT  t.id,
				t.plan_code,
				p.project_name,
				p.project_alias,
				t.job_number,
				t.management_unit,
				t.delivery_team,
				t.start_date,
				t.end_date,
				t.work_day,
				t.project_code,
				t.plan_month,
				SUM(t.plan_month_work_load) AS plan_month_work_load,
				SUM(t.plan_work_load) AS plan_work_load 
        	FROM pmt_project_month_plan t
        		LEFT JOIN pmt_project_info p ON t.project_code = p.project_code AND p.deleted = 0
        <where>
        	t.deleted = 0 
        	<if test="p.planMonth != null and p.planMonth != ''">and t.plan_month = #{p.planMonth}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">
        		and (t.project_code like concat('%', #{p.projectCode}, '%') 
        			OR p.project_name like concat('%', #{p.projectCode}, '%')
        		)
        	</if>
        	<if test="p.projectName != null and p.projectName != ''">and p.project_name like concat('%', #{p.projectName}, '%')</if>
        	<if test="p.projectAlias != null and p.projectAlias != ''">and p.project_alias like concat('%', #{p.projectAlias}, '%')</if>
        	<if test="p.managementUnit != null">and t.management_unit = #{p.managementUnit}</if>
        	<if test="p.deliveryTeam != null and p.deliveryTeam != ''">and t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">and t.job_number = #{p.jobNumber}</if>
	        ${p.params.dataScope}
        </where>
        GROUP BY t.project_code, t.plan_month
        ORDER BY t.plan_month desc, t.management_unit, p.approval_date desc
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectMonthPlanVO">
        select t.* from pmt_project_month_plan t
        <where>
        	t.deleted = 0 
        	<if test="p.deliveryTeam != null and p.deliveryTeam != ''">and t.delivery_team = #{p.deliveryTeam}</if>
        	<if test="p.planMonth != null and p.planMonth != ''">and t.plan_month = #{p.planMonth}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code = #{p.projectCode}</if>
        	<if test="p.params.pcodes != null and p.params.pcodes.size() > 0" >
        		AND t.project_code IN
        		<foreach collection="p.params.pcodes" item="pcode" open="(" separator="," close=")">
		 			#{pcode}
		        </foreach>
        	</if>
        </where>
    </select>
    
    <select id="deliverWorkLoadStat" resultType="com.swcares.pt.project.vo.DeliverWorkLoadStatVO">
   		 
    </select>
    
    <update id="deleteByCondition">
    	update pmt_project_month_plan t set t.deleted = 1 
    	<where>
    		<if test="p.planCode != null">and t.plan_code = #{p.planCode}</if>
    		<if test="p.projectCode != null">and t.project_code = #{p.projectCode}</if>
    	</where>
    </update>
    
    <delete id="removeByCondition">
    	DELETE FROM pmt_project_month_plan t 
    	<where>
    		<if test="p.mpCodes != null and p.mpCodes != ''">
    			AND t.month_plan_code IN 
    			<foreach collection="p.mpCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    		<if test="p.pCodes != null and p.pCodes != ''">
    			AND t.project_code IN 
    			<foreach collection="p.pCodes" item="code" open="(" separator="," close=")">
		 			#{code}
		        </foreach>
    		</if>
    	</where>
    </delete>
    
</mapper>
