<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.WeeklyItemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.vo.WeeklyItemsVO">
        <result column="id" 				property="id" />
        <result column="weekly_id" 			property="weeklyId" />
        <result column="plan_code" 			property="planCode" />
        <result column="project_code" 		property="projectCode" />
        <result column="milestone_code" 	property="milestoneCode" />
        <result column="current_week_ev" 	property="currentWeekEv" />
        <result column="last_week_total_ev" property="lastWeekTotalEv" />
        <result column="week_total_ev" 		property="weekTotalEv" />
        <result column="week_total_pv" 		property="weekTotalPv" />
        <result column="pace_offset" 		property="paceOffset" />
        <result column="pace_risk" 			property="paceRisk" />
        <result column="created_by" 		property="createdBy" />
        <result column="created_time" 		property="createdTime" />
        <result column="updated_by" 		property="updatedBy" />
        <result column="updated_time" 		property="updatedTime" />
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.WeeklyItemsVO">
        SELECT * FROM pmt_weekly_items
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.WeeklyItemsVO">
        SELECT t.* FROM pmt_weekly_items t
        <where>
        	t.deleted = 0 
        	<if test="p.planCode != null and p.planCode != ''">AND t.plan_code = #{p.planCode}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">AND t.project_code = #{p.projectCode}</if>
        	<if test="p.milestoneCode != null and p.milestoneCode != ''">AND t.milestone_code = #{p.milestoneCode}</if>
        	<if test="p.params.weeklyIds != null and p.params.weeklyIds.size() > 0" >
        		AND t.weekly_id IN
        		<foreach collection="p.params.weeklyIds" item="weeklyId" open="(" separator="," close=")">
		 			#{weeklyId}
		        </foreach>
        	</if>
        </where>
    </select>
    
</mapper>
