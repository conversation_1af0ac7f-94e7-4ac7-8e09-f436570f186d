<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.project.mapper.ProjectRiskTrackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.project.entity.ProjectRiskTrackDO">
        <result column="id" 				property="id" />
        <result column="project_code" 		property="projectCode" />
        <result column="risk_name" 			property="riskName" />
        <result column="risk_type" 			property="riskType" />
        <result column="risk_state" 		property="riskState" />
        <result column="description" 		property="description" />
        <result column="close_date" 		property="closeDate" />
        <result column="created_by" 		property="createdBy" />
        <result column="created_time" 		property="createdTime" />
        <result column="updated_by" 		property="updatedBy" />
        <result column="updated_time" 		property="updatedTime" />
    </resultMap>

    <select id="page" resultType="com.swcares.pt.project.vo.ProjectRiskTrackVO">
        select t.* from pmt_project_risk_track t
        <where>
        	t.deleted = 0 
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code = #{p.projectCode}</if>
        </where>
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.project.vo.ProjectRiskTrackVO">
        select t.* from pmt_project_risk_track t
        <where>
        	t.deleted = 0 
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code = #{p.projectCode}</if>
        </where>
    </select>
    
</mapper>
