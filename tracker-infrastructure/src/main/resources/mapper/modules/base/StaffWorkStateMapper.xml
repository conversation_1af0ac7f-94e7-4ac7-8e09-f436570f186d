<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.base.mapper.StaffWorkStateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.base.vo.StaffWorkStateVO">
        <result column="id" 			property="id" 			/>
        <result column="job_number" 	property="jobNumber" 	/>
        <result column="year_months" 	property="yearMonths" 	/>
        <result column="work_state" 	property="workState" 	/>
        <result column="work_load" 		property="workLoad" 	/>
        <result column="created_by" 	property="createdBy" 	/>
        <result column="created_time" 	property="createdTime" 	/>
        <result column="updated_by" 	property="updatedBy" 	/>
        <result column="updated_time" 	property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.base.vo.StaffWorkStateVO">
        select  t.*, 
        		r.real_name,
        		r.job_name,
        		r.team_name
        	from pmt_staff_work_state t
        		left join pmt_staff_resources r on r.job_number = t.job_number
        <where>
        	t.deleted = 0 
        	<if test="p.yearMonths != null and p.yearMonths != ''">and t.year_month = #{p.yearMonths}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">and t.job_number like concat('%', #{p.jobNumber}, '%')</if>
        	<if test="p.workState != null">and t.work_state = #{p.workState}</if>
        	<if test="p.teamId != null">and r.team_id = #{p.teamId}</if>
        	<if test="p.jobId != null">and r.job_id = #{p.jobId}</if>
        	<if test="p.realName != null and p.realName != ''">and r.real_name like concat('%', #{p.realName}, '%')</if>
        </where>
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.base.vo.StaffWorkStateVO">
        select t.* from pmt_staff_work_state t
        	left join pmt_staff_resources r on t.job_number = r.job_number
        <where>
        	t.deleted = 0 
        	<if test="p.statMonth != null and p.statMonth != ''">and t.year_months = #{p.statMonth}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">and t.job_number = #{p.jobNumber}</if>
        	<if test="p.workState != null">and t.work_state = #{p.workState}</if>
        	<if test="p.teamId != null">and r.team_id = #{p.teamId}</if>
        </where>
    </select>
    
    <select id="selectWorkStateStat" resultType="com.swcares.pt.base.vo.StaffWorkStateStatVO">
    	SELECT 
			DATE_FORMAT(concat(t.year_months, '-01'), '%m') AS stat_month,
			SUM(CASE WHEN t.work_state = 10 THEN 1 ELSE 0 END) duty_number,
			SUM(CASE WHEN t.work_state != 10 THEN 1 ELSE 0 END) not_duty_number
		FROM pmt_staff_work_state t 
		WHERE t.year_months BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
		GROUP BY t.year_months
    </select>
    
</mapper>
