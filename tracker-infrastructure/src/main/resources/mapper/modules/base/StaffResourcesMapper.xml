<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.base.mapper.StaffResourcesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.base.vo.StaffResourcesVO">
        <result column="id" 				property="id" 			/>
        <result column="real_name" 			property="realName" 	/>
        <result column="job_number" 		property="jobNumber" 	/>
        <result column="team_id" 			property="teamId" 		/>
        <result column="team_name" 			property="teamName" 	/>
        <result column="job_id" 			property="jobId" 		/>
        <result column="job_name" 			property="jobName" 		/>
        <result column="staff_state" 		property="staffState" 	/>
        <result column="entry_date" 		property="entryDate" 	/>
        <result column="depart_date" 		property="departDate" 	/>
        <result column="remark" 			property="remark" 		/>
        <result column="created_by" 		property="createdBy" 	/>
        <result column="created_time" 		property="createdTime" 	/>
        <result column="updated_by" 		property="updatedBy" 	/>
        <result column="updated_time" 		property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.base.vo.StaffResourcesVO">
        select t.* from pmt_staff_resources t
        <where>
        	t.deleted = 0 
        	<if test="p.jobId != null">and t.job_id = #{p.jobId}</if>
        	<if test="p.teamId != null">and t.team_id = #{p.teamId}</if>
        	<if test="p.staffState != null">and t.staff_state = #{p.staffState}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">and t.job_number like concat('%', #{p.jobNumber}, '%')</if>
        	<if test="p.realName != null and p.realName != ''">and t.real_name like concat('%', #{p.realName}, '%')</if>
        </where>
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.base.vo.StaffResourcesVO">
    	select t.* from pmt_staff_resources t
        <where>
        	t.deleted = 0 
        	<if test="p.jobId != null">and t.job_id = #{p.jobId}</if>
        	<if test="p.teamId != null">and t.team_id = #{p.teamId}</if>
        	<if test="p.staffState != null">and t.staff_state = #{p.staffState}</if>
        </where>
    </select>
    
    <select id="selectStaffJobLevelStat" resultType="com.swcares.pt.base.vo.StaffJobStatVO">
    	SELECT 
			t.job_id,
			t.job_name,
			SUM(CASE WHEN t.job_level_id = 10 THEN t.staff_number ELSE 0 END) primary_count,
			SUM(CASE WHEN t.job_level_id = 20 THEN t.staff_number ELSE 0 END) middle_count,
			SUM(CASE WHEN t.job_level_id = 30 THEN t.staff_number ELSE 0 END) senior_count
			FROM pmt_staff_job_level t WHERE t.job_id = 10
		UNION
		SELECT 
			t.job_id,
			t.job_name,
			SUM(CASE WHEN t.job_level_id = 10 THEN t.staff_number ELSE 0 END) primary_count,
			SUM(CASE WHEN t.job_level_id = 20 THEN t.staff_number ELSE 0 END) middle_count,
			SUM(CASE WHEN t.job_level_id = 30 THEN t.staff_number ELSE 0 END) senior_count
			FROM pmt_staff_job_level t WHERE t.job_id = 20
    </select>
    
    <select id="selectStaffResOverview" resultType="com.swcares.pt.base.vo.StaffResOverviewVO">
    	SELECT 
			COUNT(1) AS year_staff_total,
			(
				SELECT 
					ROUND(SUM(w.work_load) / 100, 3) AS work_load
				FROM pmt_staff_work_state w 
					WHERE w.year_months BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12')
			) AS year_work_load
		FROM pmt_staff_resources t 
			WHERE t.job_id IN (10, 20) 
				  AND (t.entry_date &lt;= concat(#{p.projectYear}, '-12-31') OR t.entry_date IS NULL ) 
				  AND (t.depart_date &gt; concat(#{p.projectYear}, '-01-01') OR t.depart_date IS NULL)
    </select>
    
</mapper>
