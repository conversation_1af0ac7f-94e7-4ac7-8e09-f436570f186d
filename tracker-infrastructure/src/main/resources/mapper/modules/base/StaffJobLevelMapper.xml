<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.base.mapper.StaffJobLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.base.vo.StaffJobLevelVO">
        <result column="id" 				property="id" 			/>
        <result column="job_id" 			property="jobId" 		/>
        <result column="job_name" 			property="jobName" 		/>
        <result column="job_level_id" 		property="jobLevelId" 	/>
        <result column="job_level_name" 	property="jobLevelName" />
        <result column="staff_number" 		property="staffNumber" 	/>
        <result column="created_by" 		property="createdBy" 	/>
        <result column="created_time" 		property="createdTime" 	/>
        <result column="updated_by" 		property="updatedBy" 	/>
        <result column="updated_time" 		property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.base.vo.StaffJobLevelVO">
        select t.* from pmt_staff_job_level t
        <where>
        	t.deleted = 0 
        	<if test="p.jobId != null">and t.job_id = #{p.jobId}</if>
        	<if test="p.jobLevelId != null">and t.job_level_id = #{p.jobLevelId}</if>
        </where>
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.base.vo.StaffJobLevelVO">
    	select t.* from pmt_staff_job_level t
        <where>
        	t.deleted = 0 
        	<if test="p.jobId != null">and t.job_id = #{p.jobId}</if>
        	<if test="p.jobLevelId != null">and t.job_level_id = #{p.jobLevelId}</if>
        </where>
    </select>
    
</mapper>
