<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.base.mapper.StaffWorkLoadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.base.vo.StaffWorkLoadVO">
        <result column="id" 			property="id" 			/>
        <result column="job_number"	 	property="jobNumber" 	/>
        <result column="year_months" 	property="yearMonths" 	/>
        <result column="project_year" 	property="projectYear" 	/>
        <result column="project_code" 	property="projectCode" 	/>
        <result column="project_name" 	property="projectName" 	/>
        <result column="capacity" 		property="capacity" 	/>
        <result column="data_type" 		property="dataType" 	/>
        <result column="created_by" 	property="createdBy" 	/>
        <result column="created_time" 	property="createdTime" 	/>
        <result column="updated_by" 	property="updatedBy" 	/>
        <result column="updated_time" 	property="updatedTime" 	/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.base.vo.StaffWorkLoadVO">
        select  t.*, 
        		r.real_name,
        		r.job_name,
        		r.team_name 
        	from pmt_staff_work_load t 
        		left join pmt_staff_resources r on r.job_number = t.job_number
        <where>
        	t.deleted = 0 
        	<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
        	<if test="p.yearMonths != null and p.yearMonths != ''">and t.year_months = #{p.yearMonths}</if>
        	<if test="p.jobNumber != null and p.jobNumber != ''">and t.job_number like concat('%', #{p.jobNumber}, '%')</if>
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code like concat('%', #{p.projectCode}, '%')</if>
        	<if test="p.projectName != null and p.projectName != ''">and t.project_name like concat('%', #{p.projectName}, '%')</if>
        	<if test="p.dataType != null">and t.data_type = #{p.dataType}</if>
        </where>
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.base.vo.StaffWorkLoadVO">
        select t.* from pmt_staff_work_load t
        <where>
        	t.deleted = 0 
        	<if test="p.jobNumber != null and p.jobNumber != ''">and t.job_number = #{p.jobNumber}</if>
        	<if test="p.projectYear != null and p.projectYear != ''">and t.project_year = #{p.projectYear}</if>
        	<if test="p.projectCode != null and p.projectCode != ''">and t.project_code = #{p.projectCode}</if>
        	<if test="p.dataType != null">and t.data_type = #{p.dataType}</if>
        	<if test="p.statMonth != null and p.statMonth != ''">and t.year_months = #{p.statMonth}</if>
        </where>
    </select>

    <select id="selectStaffAnalysis" resultType="com.swcares.pt.base.vo.StaffLoadAnalysisVO">
    	SELECT  
			 DATE_FORMAT(concat(t1.year_months, '-01'), '%m') AS stat_month, 
			 t1.max_load, 
			 t1.avg_load, 
			 t2.month_usage
		FROM (
			SELECT 
				t.year_months, 
				(SELECT MAX(k.capacity) AS max_load FROM(
					SELECT d.year_months, SUM(d.capacity) AS capacity FROM pmt_staff_work_load d 
								WHERE d.data_type = 1  
								GROUP BY d.job_number, d.year_months) k 
						WHERE k.year_months = t.year_months  
						GROUP BY t.year_months) AS max_load,
				ROUND(SUM(t.capacity) / COUNT(DISTINCT t.job_number), 3) AS avg_load 
			FROM pmt_staff_work_load t 
				WHERE t.data_type = 1 AND t.year_months BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12') 
				GROUP BY t.year_months 
		) t1 LEFT JOIN (
			SELECT 
				t.year_months, 
				ROUND(SUM(t.capacity) / (
					SELECT 
						 SUM(w.work_load) AS res 
					FROM pmt_staff_work_state w 
						WHERE w.year_months BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12') 
								 AND w.work_state = 10 AND w.year_months = t.year_months 
						GROUP BY w.year_months 
				), 3) * 100 AS month_usage 
			FROM pmt_staff_work_load t 
				WHERE t.data_type = 2 AND t.year_months BETWEEN concat(#{p.projectYear}, '-01') AND concat(#{p.projectYear}, '-12') 
				GROUP BY t.year_months 
		) t2 ON t1.year_months = t2.year_months order by t1.year_months 
    </select>
    
</mapper>
