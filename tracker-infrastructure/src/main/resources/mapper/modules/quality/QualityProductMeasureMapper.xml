<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.pt.quality.mapper.QualityProductMeasureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.pt.quality.vo.QualityProductMeasureVO">
        <result column="id" 					property="id" 				/>
        <result column="product_code" 			property="productCode" 		/>
        <result column="statistic_month" 		property="statisticMonth" 	/>
        <result column="statistic_date" 		property="statisticDate" 	/>
        <result column="sonar_date" 			property="sonarDate" 		/>
        <result column="security_rating" 		property="securityRating" 	/>
        <result column="security_issues" 		property="securityIssues" 	/>
        <result column="reliability_rating" 	property="reliabilityRating" />
        <result column="reliability_issues" 	property="reliabilityIssues" />
        <result column="new_maintainability_rating" property="newMaintainabilityRating" />
        <result column="maintainability_issues" property="maintainabilityIssues" />
        <result column="duplicated_lines_density" property="duplicatedLinesDensity" />
        <result column="coverage" 				property="coverage" 		/>
        <result column="ncloc" 					property="ncloc" 			/>
        <result column="annual_increment" 		property="annualIncrement" 	/>
        <result column="created_by" 			property="createdBy" 		/>
        <result column="created_time" 			property="createdTime" 		/>
        <result column="updated_by" 			property="updatedBy" 		/>
        <result column="updated_time" 			property="updatedTime" 		/>
    </resultMap>

    <select id="page" resultType="com.swcares.pt.quality.vo.QualityProductMeasureVO">
        SELECT  t.id, 
				t.product_code, 
				t.statistic_month, 
				t.statistic_date, 
				t.sonar_date, 
				t.security_rating, 
				t.security_issues, 
				t.reliability_rating, 
				t.reliability_issues, 
				t.new_maintainability_rating, 
				t.maintainability_issues, 
				t.duplicated_lines_density, 
				t.coverage, 
				t.ncloc, 
				t.annual_increment, 
				t.created_by, 
				t.created_time, 
				t.updated_by, 
				t.updated_time
		FROM quality_product_measure t 
        <where>
        	t.deleted = 0 
        	<if test="p.productCode != null and p.productCode != ''">AND t.product_code = #{p.productCode}</if>
        	<if test="p.statisticMonth != null">AND t.statistic_month = #{p.statisticMonth}</if>
        	<if test="p.statisticDate != null">AND t.statistic_date = #{p.statisticDate}</if>
        	<if test="p.sonarDate != null">AND t.sonar_date = #{p.sonarDate}</if>
        	<if test="p.securityRating != null and p.securityRating != ''">AND t.security_rating = #{p.securityRating}</if>
        	<if test="p.reliabilityRating != null and p.reliabilityRating != ''">AND t.reliability_rating = #{p.reliabilityRating}</if>
        	<if test="p.newMaintainabilityRating != null and p.newMaintainabilityRating != ''">AND t.new_maintainability_rating = #{p.newMaintainabilityRating}</if>
        </where>
        ORDER BY t.statistic_date DESC, t.product_code ASC
    </select>
    
    <select id="selectByCondition" resultType="com.swcares.pt.quality.vo.QualityProductMeasureVO">
        SELECT t.* FROM quality_product_measure t
        <where>
        	t.deleted = 0 
        	<if test="p.productCode != null and p.productCode != ''">AND t.product_code = #{p.productCode}</if>
        	<if test="p.statisticMonth != null">AND t.statistic_month = #{p.statisticMonth}</if>
        	<if test="p.statisticDate != null">AND t.statistic_date = #{p.statisticDate}</if>
        	<if test="p.sonarDate != null">AND t.sonar_date = #{p.sonarDate}</if>
        	<if test="p.securityRating != null and p.securityRating != ''">AND t.security_rating = #{p.securityRating}</if>
        	<if test="p.reliabilityRating != null and p.reliabilityRating != ''">AND t.reliability_rating = #{p.reliabilityRating}</if>
        	<if test="p.newMaintainabilityRating != null and p.newMaintainabilityRating != ''">AND t.new_maintainability_rating = #{p.newMaintainabilityRating}</if>
        </where>
        ORDER BY t.statistic_date DESC, t.product_code ASC
    </select>
    
</mapper>
