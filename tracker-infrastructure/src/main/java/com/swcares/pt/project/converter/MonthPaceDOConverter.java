/**  
 * All rights Reserved, Designed By <br>
 * Title：StaffResourcesDOConverter.java <br>
 * Package：com.swcares.pt.base.data <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectMonthPace;
import com.swcares.pt.project.domain.factory.ProjectMonthPaceFactory;
import com.swcares.pt.project.entity.ProjectMonthPaceDO;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;

/**   
 * ClassName：com.swcares.pt.base.data.MonthPaceDOConverter <br>
 * Description：项目月进度DO转换器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface MonthPaceDOConverter {

	MonthPaceDOConverter INSTANCE = Mappers.getMapper(MonthPaceDOConverter.class);
	
	@Mapping(source = "user.username", target = "createdBy")
	@Mapping(source = "user.username", target = "updatedBy")
	@Mapping(target = "deleted", ignore = true)
	ProjectMonthPaceDO toDO(ProjectMonthPace res);
	
	@Mapping(target = "projectAlias", ignore = true)
	@Mapping(target = "projectName", ignore = true)
	ProjectMonthPaceVO toVO(ProjectMonthPaceDO pace);
	
	default ProjectMonthPace fromDO(ProjectMonthPaceDO resDO) {
		ProjectMonthPace res = ProjectMonthPaceFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	void update(ProjectMonthPaceDO source, @MappingTarget ProjectMonthPace res);
	
	
	default List<ProjectMonthPaceDO> toDOs(List<ProjectMonthPace> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(MonthPaceDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
