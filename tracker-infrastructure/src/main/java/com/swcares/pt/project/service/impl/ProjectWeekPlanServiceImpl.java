package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.dto.ProjectWeekPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectWeekPlanDO;
import com.swcares.pt.project.mapper.ProjectWeekPlanMapper;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.service.ProjectWeekPlanService;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectWeekPlanServiceImpl <br>
 * Description：项目周计划 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectWeekPlanServiceImpl extends BaseServiceImpl<ProjectWeekPlanMapper, ProjectWeekPlanDO> implements ProjectWeekPlanService {

    @Override
    public IPage<ProjectWeekPlanVO> page(ProjectWeekPlanPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectWeekPlanVO> getByCondition(ProjectWeekPlanDTO params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public boolean removeByCondition(MilestoneDelParam params) {
		return baseMapper.removeByCondition(params) > 0;
	}
    
    
}
