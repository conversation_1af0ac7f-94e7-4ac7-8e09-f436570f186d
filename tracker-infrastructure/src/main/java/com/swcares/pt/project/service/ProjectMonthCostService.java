package com.swcares.pt.project.service;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectMonthCostPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthCostDO;
import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.vo.ManagementUnitMonthStatVO;
import com.swcares.pt.project.vo.ProjectAnalysisStatVO;
import com.swcares.pt.project.vo.ProjectMonthCostVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectMonthCostService <br>
 * Description：项目月成本 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
public interface ProjectMonthCostService extends BaseService<ProjectMonthCostDO> {

    /**
     * Description：分页查询 <br>
     * author：luojl <br>
     * date：2024-08-15 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectMonthCostVO> page(ProjectMonthCostPagedDTO dto);
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectMonthCostVO> getByCondition(QueryParam params);
    
    /**
     * Description：成本分析统计查询 <br>
     * author：罗江林 <br>
     * date：2024年8月21日 上午11:38:37 <br>
     * @param param
     * @return <br>
     */
    List<ProjectAnalysisStatVO> queryCostAnalysis(@Param("p") LargeScreenParam param);
    
    
    /**
     * Description：经营单元成本统计查询 <br>
     * author：罗江林 <br>
     * date：2024年8月21日 下午3:14:24 <br>
     * @param param
     * @return <br>
     */
    List<ManagementUnitMonthStatVO> queryMgtUnitCostStat(LargeScreenParam param);
    
}
