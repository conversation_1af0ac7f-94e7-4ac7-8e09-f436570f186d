package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.dto.ProjectMilestonePagedDTO;
import com.swcares.pt.project.entity.ProjectMilestoneDO;
import com.swcares.pt.project.mapper.ProjectMilestoneMapper;
import com.swcares.pt.project.service.ProjectMilestoneService;
import com.swcares.pt.project.vo.ProjectMilestoneVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectMilestoneServiceImpl <br>
 * Description：项目里程碑 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-12 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectMilestoneServiceImpl extends BaseServiceImpl<ProjectMilestoneMapper, ProjectMilestoneDO> implements ProjectMilestoneService {

    @Override
    public IPage<ProjectMilestoneVO> page(ProjectMilestonePagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectMilestoneVO> getByCondition(ProjectMilestoneDTO params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public ProjectMilestoneVO getByMilestoneCode(String milestoneCode) {
		List<ProjectMilestoneVO> datas = getByCondition(ProjectMilestoneDTO.builder().milestoneCode(milestoneCode).build());
		if(ListUtils.isNotEmpty(datas) && datas.size() == 1) {
			return datas.get(0);
		}
		return null;
	}

	@Override
	public boolean removeByCondition(ProjectMilestoneDTO params) {
		return baseMapper.deleteByCondition(params) > 0;
	}

	@Override
	public List<ProjectMilestoneVO> getByProjectState(ProjectMilestoneDTO params) {
		return baseMapper.selectByProjectState(params);
	}
    
}
