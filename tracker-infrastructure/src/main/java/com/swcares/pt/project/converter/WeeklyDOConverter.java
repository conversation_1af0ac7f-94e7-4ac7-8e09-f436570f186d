/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyDOConverter.java <br>
 * Package：com.swcares.pt.project.converter <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.factory.ProjectWeeklyFactory;
import com.swcares.pt.project.entity.ProjectWeeklyDO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

/**   
 * ClassName：com.swcares.pt.project.converter.WeeklyDOConverter <br>
 * Description：项目周报DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface WeeklyDOConverter {

	WeeklyDOConverter INSTANCE = Mappers.getMapper(WeeklyDOConverter.class);

	ProjectWeeklyDO toDO(ProjectWeekly Weekly);
	
	@Mapping(target = "projectName", ignore = true)
	@Mapping(target = "cycle", ignore = true)
	@Mapping(target = "riskNum", ignore = true)
	ProjectWeeklyVO toVO(ProjectWeeklyDO Weekly);
	
	default ProjectWeekly fromDO(ProjectWeeklyDO resDO) {
		ProjectWeekly res = ProjectWeeklyFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "threshold", ignore = true)
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "items", ignore = true)
	@Mapping(target = "isClose", ignore = true)
	@Mapping(target = "last", ignore = true)
	void update(ProjectWeeklyDO source, @MappingTarget ProjectWeekly res);
	
	default List<ProjectWeeklyDO> toDOs(List<ProjectWeekly> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(WeeklyDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
