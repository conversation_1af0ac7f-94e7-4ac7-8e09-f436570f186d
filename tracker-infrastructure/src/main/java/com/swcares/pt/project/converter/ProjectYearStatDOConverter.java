/**  
 * All rights Reserved, Designed By <br>
 * Title：YearStatDOConverter.java <br>
 * Package：com.swcares.pt.project.converter <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectYearStat;
import com.swcares.pt.project.domain.factory.ProjectYearStatFactory;
import com.swcares.pt.project.entity.ProjectYearStatDO;
import com.swcares.pt.project.vo.ProjectYearStatVO;

/**   
 * ClassName：com.swcares.pt.project.converter.YearStatDOConverter <br>
 * Description：项目年统计DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface ProjectYearStatDOConverter {

	ProjectYearStatDOConverter INSTANCE = Mappers.getMapper(ProjectYearStatDOConverter.class);

	@Mapping(target = "deleted", ignore = true)
	ProjectYearStatDO toDO(ProjectYearStat YearStat);
	
	ProjectYearStatVO toVO(ProjectYearStatDO YearStat);
	
	default ProjectYearStat fromDO(ProjectYearStatDO resDO) {
		ProjectYearStat res = ProjectYearStatFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "milestones", ignore = true)
	void update(ProjectYearStatDO source, @MappingTarget ProjectYearStat res);
	
	default List<ProjectYearStatDO> toDOs(List<ProjectYearStat> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(ProjectYearStatDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
