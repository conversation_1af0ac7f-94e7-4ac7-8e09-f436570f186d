package com.swcares.pt.project.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectWeeklyPagedDTO;
import com.swcares.pt.project.entity.ProjectWeeklyDO;
import com.swcares.pt.project.param.ProjectWeeklyParam;
import com.swcares.pt.project.vo.ProjectWeeklyDetailVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectWeeklyService <br>
 * Description：项目周报 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
public interface ProjectWeeklyService extends BaseService<ProjectWeeklyDO> {

	/**
	 * Description：根据ID获取周报及明细项 <br>
	 * author：罗江林 <br>
	 * date：2025年3月20日 下午2:42:56 <br>
	 * @param id
	 * @return <br>
	 */
	ProjectWeeklyDetailVO getByWeeklyId(Long id);
	
    /**
     * Title：page <br>
     * Description：项目周报分页查询 <br>
     * author：luojl <br>
     * date：2025-03-14 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectWeeklyVO> page(ProjectWeeklyPagedDTO dto);
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectWeeklyVO> getByCondition(ProjectWeeklyParam params);
    
    /**
     * Description：根据查询条件获取周报数据集，包含明细项 <br>
     * author：罗江林 <br>
     * date：2025年3月19日 下午1:40:58 <br>
     * @param params
     * @return <br>
     */
    List<ProjectWeeklyDetailVO> getByConditionContainItems(ProjectWeeklyParam params);
    
}
