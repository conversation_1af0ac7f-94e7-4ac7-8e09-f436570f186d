/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectRiskTrackRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.pt.project.converter.RiskTrackDOConverter;
import com.swcares.pt.project.domain.ProjectRiskTrack;
import com.swcares.pt.project.dto.ProjectRiskTrackDTO;
import com.swcares.pt.project.service.ProjectRiskTrackService;
import com.swcares.pt.project.vo.ProjectRiskTrackVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectRiskTrackRepositoryImpl <br>
 * Description：项目周计划仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectRiskTrackRepositoryImpl implements ProjectRiskTrackRepository {

	@Autowired
	private ProjectRiskTrackService riskTrackService;
	
	@Override
	public boolean save(ProjectRiskTrack data) {
		return riskTrackService.saveOrUpdate(RiskTrackDOConverter.INSTANCE.toDO(data));
	}

	@Override
	public boolean batchSave(List<ProjectRiskTrack> datas) {
		return riskTrackService.saveOrUpdateBatch(RiskTrackDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<ProjectRiskTrackVO> getByCondition(ProjectRiskTrackDTO params) {
		return riskTrackService.getByCondition(params);
	}

	@Override
	public List<ProjectRiskTrackVO> getRiskTrackByProjectCode(String projectCode) {
		return getByCondition(ProjectRiskTrackDTO.builder().projectCode(projectCode).build());
	}

}
