/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyItemsRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.project.converter.WeeklyItemsDOConverter;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.dto.WeeklyItemsDTO;
import com.swcares.pt.project.service.WeeklyItemsService;
import com.swcares.pt.project.vo.WeeklyItemsVO;

/**   
 * ClassName：com.swcares.pt.project.repository.WeeklyItemsRepositoryImpl <br>
 * Description：项目周报仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class WeeklyItemsRepositoryImpl implements WeeklyItemsRepository {

	@Autowired
	private WeeklyItemsService weeklyItemsService;
	
	@Override
	public boolean save(WeeklyItems data) {
		return weeklyItemsService.saveOrUpdate(WeeklyItemsDOConverter.INSTANCE.toDO(data));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchSave(List<WeeklyItems> datas) {
		return weeklyItemsService.saveOrUpdateBatch(WeeklyItemsDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<WeeklyItemsVO> getByCondition(WeeklyItemsDTO params) {
		return weeklyItemsService.getByCondition(params);
	}

	@Override
	public Map<Long, List<WeeklyItemsVO>> getByWeeklyIds(List<Long> weeklyIds) {
		if(ListUtils.isEmpty(weeklyIds)) {
			return MapUtils.newHashMap();
		}
		Map<String, Object> params = MapUtils.newHashMap();
		params.put("weeklyIds", weeklyIds);
		List<WeeklyItemsVO> items = getByCondition(WeeklyItemsDTO.builder().params(params).build());
		if(ListUtils.isNotEmpty(items)) {
			return items.stream().collect(Collectors.groupingBy(i -> i.getWeeklyId()));
		}
		return MapUtils.newHashMap();
	}

}
