/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectInfoRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:25:50 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.enums.ProjectStateEnum;
import com.swcares.pt.project.converter.MilestoneDOConverter;
import com.swcares.pt.project.converter.ProjectInfoDOConverter;
import com.swcares.pt.project.domain.ProjectInfo;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.dto.ProjectInfoDTO;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.entity.ProjectInfoDO;
import com.swcares.pt.project.service.ProjectInfoService;
import com.swcares.pt.project.vo.ProjectInfoVO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectInfoRepositoryImpl <br>
 * Description：项目信息仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:25:50 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectInfoRepositoryImpl implements ProjectInfoRepository {

	@Autowired
	private ProjectInfoService projectInfoService;
	@Autowired
	private ProjectMilestoneRepository milestoneRepository;
	@Autowired
	private ProjectWeeklyRepository weeklyRepository;
	
	@Override
	public boolean save(ProjectInfo pi) {
		return projectInfoService.saveOrUpdate(ProjectInfoDOConverter.INSTANCE.toDO(pi));
	}

	@Override
	public boolean batchSave(List<ProjectInfo> pis) {
		return projectInfoService.saveOrUpdateBatch(ProjectInfoDOConverter.INSTANCE.toDOs(pis), BATCH_SIZE);
	}

	@Override
	public List<ProjectInfoVO> getByCondition(ProjectInfoDTO params) {
		return projectInfoService.getByCondition(params);
	}

	@Override
	public Map<String, ProjectInfoVO> getProjectByState(ProjectStateEnum state) {
		ProjectInfoDTO params = ProjectInfoDTO.builder().projectState(state.getCode()).build();
		List<ProjectInfoVO> pis = getByCondition(params);
		if(ListUtils.isNotEmpty(pis)) {
			return pis.stream().collect(Collectors.toMap(ProjectInfoVO::getProjectCode, p -> p));
		}
		return MapUtils.newHashMap();
	}

	@Override
	public Map<String, ProjectInfoVO> getUnderwayProject() {
		return getUnderwayProject(ListUtils.newArrayList());
	}
	
	@Override
	public Map<String, ProjectInfoVO> getUnderwayProject(List<String> pcodes) {
		Map<String, Object> params = MapUtils.newHashMap();
		params.put("pcodes", pcodes);
		List<ProjectInfoVO> pis = getByCondition(ProjectInfoDTO.builder().params(params).projectState(ProjectStateEnum.underway.getCode()).build());
		Map<String, ProjectInfoVO> originals = pis.stream().collect(Collectors.toMap(p -> p.getProjectCode(), v -> v));
		return originals;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeProject(ProjectInfo pi) {
		List<ProjectWeeklyVO> weeklys = weeklyRepository.loadWeeklys(pi.getProjectCode());
		if(ListUtils.isNotEmpty(weeklys)) {
			throw new BusinessException(CommonErrors.CUSTOM_ERROR, "该项目已有周报，不能删除！");
		}
		List<ProjectMilestoneVO> milestones = milestoneRepository.getByCondition(ProjectMilestoneDTO.builder().projectCode(pi.getProjectCode()).build());
		if(ListUtils.isNotEmpty(milestones)) {
			milestones.forEach(m -> {
				ProjectMilestone pm = MilestoneDOConverter.INSTANCE.fromVO(m);
				pm.remove();
			});
		}
		return projectInfoService.logicRemoveById(pi.getId());
	}

	@Override
	public ProjectInfo getById(Long projectId) {
		ProjectInfoDO pi = projectInfoService.getById(projectId);
		return ProjectInfoDOConverter.INSTANCE.fromDO(pi);
	}

}
