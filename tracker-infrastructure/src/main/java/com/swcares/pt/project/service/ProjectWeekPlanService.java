package com.swcares.pt.project.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.dto.ProjectWeekPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectWeekPlanDO;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectWeekPlanService <br>
 * Description：项目周计划 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
public interface ProjectWeekPlanService extends BaseService<ProjectWeekPlanDO> {

    /**
     * Description：项目周计划分页查询 <br>
     * author：luojl <br>
     * date：2025-03-14 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectWeekPlanVO> page(ProjectWeekPlanPagedDTO dto);
    
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectWeekPlanVO> getByCondition(ProjectWeekPlanDTO params);
    
    /**
     * Description：根据里程碑、月计划、项目编号进行物理删除 <br>
     * author：罗江林 <br>
     * date：2025年3月17日 下午5:23:57 <br>
     * @param params
     * @return <br>
     */
    boolean removeByCondition(MilestoneDelParam params);
    
}
