/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectWeeklyRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.enums.WeeklyStateEnum;
import com.swcares.pt.project.converter.WeeklyDOConverter;
import com.swcares.pt.project.domain.ProjectWeekly;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.entity.ProjectWeeklyDO;
import com.swcares.pt.project.listener.WeeklyStateEvent;
import com.swcares.pt.project.param.ProjectWeeklyParam;
import com.swcares.pt.project.service.ProjectWeeklyService;
import com.swcares.pt.project.vo.ProjectWeeklyDetailVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;
import com.swcares.pt.project.vo.WeeklyItemsVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectWeeklyRepositoryImpl <br>
 * Description：项目周报仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectWeeklyRepositoryImpl implements ProjectWeeklyRepository {

	@Autowired
	private ProjectWeeklyService weeklyService;
	@Autowired
	private WeeklyItemsRepository itemsRepository;
	@Autowired
    private ApplicationContext applicationContext;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean save(ProjectWeekly data) {
		itemsRepository.batchSave(data.getItems());
		ProjectWeekly last = data.getLast();
		if(last != null && last.getIsClose()) {
			weeklyService.saveOrUpdate(WeeklyDOConverter.INSTANCE.toDO(last));
		}
		boolean rs = weeklyService.saveOrUpdate(WeeklyDOConverter.INSTANCE.toDO(data));
		ProjectWeekly next = data.getNextWeekly();
		if(next != null) {
			batchSaveWeeklys(ListUtils.newArrayList(next));
		}
		applicationContext.publishEvent(WeeklyStateEvent.builder().weekly(data).build());
		return rs;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchSave(List<ProjectWeekly> datas) {
		return batchSaveWeeklys(datas);
	}

	@Override
	public List<ProjectWeeklyVO> getByCondition(ProjectWeeklyParam params) {
		return weeklyService.getByCondition(params);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchSaveWeeklys(List<ProjectWeekly> weeklys) {
		List<Long> weeklyIds = findOriginalWeekly(weeklys);
		List<ProjectWeeklyDO> weeklyDOs = WeeklyDOConverter.INSTANCE.toDOs(weeklys);
		weeklyService.saveOrUpdateBatch(weeklyDOs);
		List<WeeklyItems> items = handleWeeklyItem(weeklyIds, weeklyDOs, weeklys);
		return itemsRepository.batchSave(items);
	}
	
	private List<WeeklyItems> handleWeeklyItem(List<Long> weeklyIds, List<ProjectWeeklyDO> weeklyDOs, List<ProjectWeekly> weeklys){
		Map<String, ProjectWeeklyDO> dos = weeklyDOs.stream().collect(Collectors.toMap(w -> w.getProjectCode(), v -> v));
		List<WeeklyItems> items = ListUtils.newArrayList();
		for (ProjectWeekly w : weeklys) {
			w.getItems().forEach(d -> {
				d.setWeeklyId(dos.get(w.getProjectCode()).getId());
				items.add(d);
			});
		}
		findOriginalWeeklyItem(weeklyIds, items);
		return items;
	}
	
	/**
	 * Description：加载匹配已有周报明细项 <br>
	 * author：罗江林 <br>
	 * date：2025年3月19日 上午10:43:38 <br>
	 * @param weeklyIds
	 * @param items <br>
	 */
	private void findOriginalWeeklyItem(List<Long> weeklyIds, List<WeeklyItems> items) {
		if(ListUtils.isEmpty(items)) {
			return;
		}
		Map<Long, List<WeeklyItemsVO>> rs = itemsRepository.getByWeeklyIds(weeklyIds);
		if(MapUtils.isEmpty(rs)) {
			return;
		}
		List<WeeklyItemsVO> datas = ListUtils.newArrayList();
		for (Long key : rs.keySet()) {
			datas.addAll(rs.get(key));
		}
		
		Map<String, WeeklyItemsVO> originals = datas.stream().collect(Collectors.toMap(w -> w.getMilestoneCode(), v -> v));
		items.forEach(i -> {
			WeeklyItemsVO vo = originals.get(i.getMilestoneCode());
			if(vo != null) {
				i.setId(vo.getId());
			}
		}); 
	}
	
	/**
	 * Description：加载指定周的周报 <br>
	 * author：罗江林 <br>
	 * date：2025年3月19日 上午9:56:05 <br>
	 * @param weekly
	 * @return <br>
	 */
	private List<Long> findOriginalWeekly(List<ProjectWeekly> weeklys){
		ProjectWeekly weekly = weeklys.get(0);
		ProjectWeeklyParam params = ProjectWeeklyParam.builder().year(weekly.getYear()).yearWeekNum(weekly.getYearWeekNum()).build();
		List<ProjectWeeklyVO> originals = getByCondition(params);
		List<Long> weeklyIds = ListUtils.newArrayList();
		if(ListUtils.isNotEmpty(originals)) {
			Map<String, ProjectWeeklyVO> vos = originals.stream().collect(Collectors.toMap(w -> w.getProjectCode(), v -> v));
			weeklys.forEach(w -> {
				ProjectWeeklyVO vo = vos.get(w.getProjectCode());
				if(vo != null) {
					w.setId(vo.getId());
					weeklyIds.add(vo.getId());
				}
			});
		}
		return weeklyIds;
	}

	@Override
	public List<ProjectWeeklyDetailVO> getByConditionContainItem(ProjectWeeklyParam params) {
		return weeklyService.getByConditionContainItems(params);
	}

	@Override
	public ProjectWeeklyDetailVO getByWeelyId(Long weeklyId) {
		return weeklyService.getByWeeklyId(weeklyId);
	}

	@Override
	public ProjectWeeklyVO getWeekly(String projectCode, Integer year, Integer weekNum) {
		List<ProjectWeeklyVO> weeklys = getByCondition(ProjectWeeklyParam.builder()
				.yearWeekNum(weekNum)
				.year(year)
				.projectCode(projectCode)
				.build());
		if(ListUtils.isNotEmpty(weeklys)) {
			return weeklys.get(0);
		}
		return null;
	}

	@Override
	public ProjectWeeklyVO getById(Long weeklyId) {
		ProjectWeeklyDO weekly = weeklyService.getById(weeklyId);
		return WeeklyDOConverter.INSTANCE.toVO(weekly);
	}

	@Override
	public List<ProjectWeeklyVO> loadWeeklys(String projectCode) {
		Map<String, Object> params = MapUtils.newHashMap();
		params.put("states", ListUtils.newArrayList(WeeklyStateEnum.wrote.getCode(), WeeklyStateEnum.closed.getCode()));
		ProjectWeeklyParam build = ProjectWeeklyParam.builder().projectCode(projectCode).build();
		build.setParams(params);
		return getByCondition(build);
	} 

}
