/**  
 * All rights Reserved, Designed By <br>
 * Title：RiskTrackDOConverter.java <br>
 * Package：com.swcares.pt.project.converter <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectRiskTrack;
import com.swcares.pt.project.domain.factory.ProjectRiskTrackFactory;
import com.swcares.pt.project.entity.ProjectRiskTrackDO;
import com.swcares.pt.project.vo.ProjectRiskTrackVO;

/**   
 * ClassName：com.swcares.pt.project.converter.RiskTrackDOConverter <br>
 * Description：项目风险DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface RiskTrackDOConverter {

	RiskTrackDOConverter INSTANCE = Mappers.getMapper(RiskTrackDOConverter.class);

	@Mapping(source = "user.username", target = "createdBy")
	@Mapping(source = "user.username", target = "updatedBy")
	@Mapping(target = "deleted", ignore = true)
	ProjectRiskTrackDO toDO(ProjectRiskTrack RiskTrack);
	
	ProjectRiskTrackVO toVO(ProjectRiskTrackDO RiskTrack);
	
	default ProjectRiskTrack fromDO(ProjectRiskTrackDO resDO) {
		ProjectRiskTrack res = ProjectRiskTrackFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	void update(ProjectRiskTrackDO source, @MappingTarget ProjectRiskTrack res);
	
	default List<ProjectRiskTrackDO> toDOs(List<ProjectRiskTrack> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(RiskTrackDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
