package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.MilestoneMonthPlanDTO;
import com.swcares.pt.project.dto.MilestoneMonthPlanPagedDTO;
import com.swcares.pt.project.entity.MilestoneMonthPlanDO;
import com.swcares.pt.project.mapper.MilestoneMonthPlanMapper;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.service.MilestoneMonthPlanService;
import com.swcares.pt.project.vo.DeliverWorkLoadStatVO;
import com.swcares.pt.project.vo.MilestoneMonthPlanVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectMonthPlanServiceImpl <br>
 * Description：里程碑月计划 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Service
public class MilestoneMonthPlanServiceImpl extends BaseServiceImpl<MilestoneMonthPlanMapper, MilestoneMonthPlanDO>
		implements MilestoneMonthPlanService {

    @Override
    public IPage<MilestoneMonthPlanVO> page(MilestoneMonthPlanPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<MilestoneMonthPlanVO> getByCondition(MilestoneMonthPlanDTO params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public List<DeliverWorkLoadStatVO> deliverWorkLoadStat(ProjectInfoParam params) {
		return baseMapper.deliverWorkLoadStat(params);
	}

	@Override
	public boolean removeByCondition(MilestoneDelParam params) {
		return baseMapper.removeByCondition(params) > 0;
	}
	
}
