package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectInfoDTO;
import com.swcares.pt.project.dto.ProjectInfoPagedDTO;
import com.swcares.pt.project.entity.ProjectInfoDO;
import com.swcares.pt.project.mapper.ProjectInfoMapper;
import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.service.ProjectInfoService;
import com.swcares.pt.project.vo.ManagementUnitRiskStatVO;
import com.swcares.pt.project.vo.ProjectInfoVO;
import com.swcares.pt.project.vo.ProjectOverviewVO;
import com.swcares.pt.project.vo.ProjectPriorityStatVO;
import com.swcares.pt.project.vo.ProjectStageStatVO;

import cn.hutool.core.date.DateUtil;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectInfoServiceImpl <br>
 * Description：项目信息 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectInfoServiceImpl extends BaseServiceImpl<ProjectInfoMapper, ProjectInfoDO>
		implements ProjectInfoService {

	@Override
    public IPage<ProjectInfoVO> page(ProjectInfoPagedDTO dto) {
		if(dto.getProjectYear() == null) {
    		dto.setProjectYear(DateUtil.date().year());
    	}
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectInfoVO> getByCondition(ProjectInfoDTO params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public ProjectPriorityStatVO priorityStat(ProjectInfoParam params) {
		return baseMapper.priorityStat(params);
	}

	@Override
	public ProjectOverviewVO projectOverviewStat(ProjectInfoParam params) {
		return baseMapper.projectOverviewStat(params);
	}

	@Override
	public List<ProjectStageStatVO> projectStageStat(ProjectInfoParam params) {
		return baseMapper.projectStageStat(params);
	}

	@Override
	public List<ManagementUnitRiskStatVO> queryProjectRiskStat(LargeScreenParam params) {
		return baseMapper.selectProjectRiskStat(params);
	}
	
}
