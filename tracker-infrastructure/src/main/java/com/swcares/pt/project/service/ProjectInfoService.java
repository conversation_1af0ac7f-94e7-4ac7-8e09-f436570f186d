package com.swcares.pt.project.service;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectInfoDTO;
import com.swcares.pt.project.dto.ProjectInfoPagedDTO;
import com.swcares.pt.project.entity.ProjectInfoDO;
import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.vo.ManagementUnitRiskStatVO;
import com.swcares.pt.project.vo.ProjectInfoVO;
import com.swcares.pt.project.vo.ProjectOverviewVO;
import com.swcares.pt.project.vo.ProjectPriorityStatVO;
import com.swcares.pt.project.vo.ProjectStageStatVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectInfoService <br>
 * Description：项目信息 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
public interface ProjectInfoService extends BaseService<ProjectInfoDO> {

    /**
     * Description：分页查询 <br>
     * author：luojl <br>
     * date：2024-08-15 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectInfoVO> page(ProjectInfoPagedDTO dto);
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectInfoVO> getByCondition(ProjectInfoDTO params);
    
    /**
     * Description：项目优先级统计查询 <br>
     * author：罗江林 <br>
     * date：2024年8月20日 下午2:11:34 <br>
     * @param params
     * @return <br>
     */
    ProjectPriorityStatVO priorityStat(@Param("p") ProjectInfoParam params);
    
    /**
     * Description：项目总览统计 <br>
     * author：罗江林 <br>
     * date：2024年8月20日 下午2:12:59 <br>
     * @param params
     * @return <br>
     */
    ProjectOverviewVO projectOverviewStat(@Param("p") ProjectInfoParam params);
    
    /**
     * Description：项目阶段统计 <br>
     * author：罗江林 <br>
     * date：2024年8月20日 下午2:12:59 <br>
     * @param params
     * @return <br>
     */
    List<ProjectStageStatVO> projectStageStat(@Param("p") ProjectInfoParam params);
    
    /**
     * Description：查询经营单元成本/进度风险 <br>
     * author：罗江林 <br>
     * date：2024年8月22日 上午11:24:45 <br>
     * @param params
     * @return <br>
     */
    List<ManagementUnitRiskStatVO> queryProjectRiskStat(@Param("p") LargeScreenParam params);
    
}
