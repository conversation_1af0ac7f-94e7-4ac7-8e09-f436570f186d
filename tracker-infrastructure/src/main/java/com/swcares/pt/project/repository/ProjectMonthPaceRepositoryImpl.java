/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPaceRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.pt.project.converter.MonthPaceDOConverter;
import com.swcares.pt.project.domain.ProjectMonthPace;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.service.ProjectMonthPaceService;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthPaceRepositoryImpl <br>
 * Description：项目月进度仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectMonthPaceRepositoryImpl implements ProjectMonthPaceRepository {

	@Autowired
	private ProjectMonthPaceService monthPaceService;
	
	@Override
	public boolean save(ProjectMonthPace data) {
		return monthPaceService.saveOrUpdate(MonthPaceDOConverter.INSTANCE.toDO(data));
	}

	@Override
	public boolean batchSave(List<ProjectMonthPace> datas) {
		return monthPaceService.saveOrUpdateBatch(MonthPaceDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<ProjectMonthPaceVO> getByCondition(QueryParam params) {
		return monthPaceService.getByCondition(params);
	}

}
