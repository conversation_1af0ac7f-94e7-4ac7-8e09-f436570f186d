/**  
 * All rights Reserved, Designed By <br>
 * Title：StaffResourcesDOConverter.java <br>
 * Package：com.swcares.pt.base.data <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectInfo;
import com.swcares.pt.project.domain.factory.ProjectInfoFactory;
import com.swcares.pt.project.entity.ProjectInfoDO;
import com.swcares.pt.project.vo.ProjectInfoVO;

/**   
 * ClassName：com.swcares.pt.base.data.StaffResourcesDOConverter <br>
 * Description：项目信息DO转换器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface ProjectInfoDOConverter {

	ProjectInfoDOConverter INSTANCE = Mappers.getMapper(ProjectInfoDOConverter.class);
	
	@Mapping(target = "deleted", ignore = true)
	ProjectInfoDO toDO(ProjectInfo res);
	
	@Mapping(target = "planCostTotal", ignore = true)
	@Mapping(target = "yearPlanCost", ignore = true)
	ProjectInfoVO toVO(ProjectInfoDO state);
	
	default ProjectInfo fromDO(ProjectInfoDO resDO) {
		ProjectInfo res = ProjectInfoFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	void update(ProjectInfoDO source, @MappingTarget ProjectInfo res);
	
	
	default List<ProjectInfoDO> toDOs(List<ProjectInfo> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(ProjectInfoDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
