/**  
 * All rights Reserved, Designed By <br>
 * Title：StaffResourcesDOConverter.java <br>
 * Package：com.swcares.pt.base.data <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectMonthCost;
import com.swcares.pt.project.domain.factory.ProjectMonthCostFactory;
import com.swcares.pt.project.entity.ProjectMonthCostDO;
import com.swcares.pt.project.vo.ProjectMonthCostVO;

/**   
 * ClassName：com.swcares.pt.base.data.MonthCostDOConverter <br>
 * Description：项目月成本DO转换器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface MonthCostDOConverter {

	MonthCostDOConverter INSTANCE = Mappers.getMapper(MonthCostDOConverter.class);
	
	@Mapping(source = "user.username", target = "createdBy")
	@Mapping(source = "user.username", target = "updatedBy")
	@Mapping(target = "deleted", ignore = true)
	ProjectMonthCostDO toDO(ProjectMonthCost res);
	
	@Mapping(target = "projectAlias", ignore = true)
	@Mapping(target = "projectName", ignore = true)
	ProjectMonthCostVO toVO(ProjectMonthCostDO cost);
	
	default ProjectMonthCost fromDO(ProjectMonthCostDO resDO) {
		ProjectMonthCost res = ProjectMonthCostFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	void update(ProjectMonthCostDO source, @MappingTarget ProjectMonthCost res);
	
	default List<ProjectMonthCostDO> toDOs(List<ProjectMonthCost> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(MonthCostDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
