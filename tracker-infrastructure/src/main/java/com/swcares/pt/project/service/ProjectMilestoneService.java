package com.swcares.pt.project.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.dto.ProjectMilestonePagedDTO;
import com.swcares.pt.project.entity.ProjectMilestoneDO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectMilestoneService <br>
 * Description：项目里程碑 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-12 <br>
 * @version v1.0 <br>
 */
public interface ProjectMilestoneService extends BaseService<ProjectMilestoneDO> {

    /**
     * Description：分页查询 <br>
     * author：luojl <br>
     * date：2025-03-12 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectMilestoneVO> page(ProjectMilestonePagedDTO dto);
    
    /**
     * Description：根据条件查询里程碑数据集 <br>
     * author：罗江林 <br>
     * date：2025年3月12日 下午3:24:16 <br>
     * @param params
     * @return <br>
     */
    List<ProjectMilestoneVO> getByCondition(ProjectMilestoneDTO params);
    
    /**
     * Description：获取项目在建设中的里程碑数据集 <br>
     * author：罗江林 <br>
     * date：2025年3月18日 下午1:51:43 <br>
     * @param params
     * @return <br>
     */
    List<ProjectMilestoneVO> getByProjectState(ProjectMilestoneDTO params);
    
    /**
     * Description：根据里程碑编号获取里程碑VO <br>
     * author：罗江林 <br>
     * date：2025年3月17日 下午5:04:20 <br>
     * @param milestoneCode
     * @return <br>
     */
    ProjectMilestoneVO getByMilestoneCode(String milestoneCode);
    
    /**
     * Description：根据里程碑编号、项目编号进行逻辑删除 <br>
     * author：罗江林 <br>
     * date：2025年3月17日 下午5:27:25 <br>
     * @param params
     * @return <br>
     */
    boolean removeByCondition(ProjectMilestoneDTO params);
    
}
