package com.swcares.pt.project.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import com.swcares.pt.project.dto.ProjectMonthPlanDTO;
import com.swcares.pt.project.dto.ProjectMonthPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthPlanDO;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.vo.DeliverWorkLoadStatVO;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**
 * ClassName：com.swcares.pt.project.pmt.mapper.ProjectMonthPlanMapper <br>
 * Description：项目月计划 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
public interface ProjectMonthPlanMapper extends BaseMapper<ProjectMonthPlanDO> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：luojl <br>
     * date：2024-08-15 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<ProjectMonthPlanVO> page(@Param("p") ProjectMonthPlanPagedDTO dto, Page<ProjectMonthPlanVO> page);
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectMonthPlanVO> selectByCondition(@Param("p") ProjectMonthPlanDTO params);
    
    /**
     * Description：交付工量需求，季度统计 <br>
     * author：罗江林 <br>
     * date：2024年8月20日 下午2:18:42 <br>
     * @return <br>
     */
    List<DeliverWorkLoadStatVO> deliverWorkLoadStat(@Param("p") ProjectInfoParam params);
    
    /**
     * Description：根据参数条件进行批量逻辑删除 <br>
     * author：罗江林 <br>
     * date：2025年3月17日 下午5:12:28 <br>
     * @param mcode
     * @return <br>
     */
    int deleteByCondition(@Param("p") ProjectMonthPlanDTO plan);
    
    /**
     * Description：根据条件物理删除里程碑月计划 <br>
     * author：罗江林 <br>
     * date：2025年5月29日 下午3:52:37 <br>
     * @param params
     * @return <br>
     */
    int removeByCondition(@Param("p") MilestoneDelParam params);
    
}
