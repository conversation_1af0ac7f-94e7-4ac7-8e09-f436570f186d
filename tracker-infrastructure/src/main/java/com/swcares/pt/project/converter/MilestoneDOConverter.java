/**  
 * All rights Reserved, Designed By <br>
 * Title：MilestoneDOConverter.java <br>
 * Package：com.swcares.pt.project.converter <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.factory.ProjectMilestoneFactory;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.entity.ProjectMilestoneDO;
import com.swcares.pt.project.vo.ProjectMilestoneVO;

/**   
 * ClassName：com.swcares.pt.project.converter.MilestoneDOConverter <br>
 * Description：里程碑DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface MilestoneDOConverter {

	MilestoneDOConverter INSTANCE = Mappers.getMapper(MilestoneDOConverter.class);

	@Mapping(target = "deleted", ignore = true)
	ProjectMilestoneDO toDO(ProjectMilestone milestone);
	
	@Mapping(target = "projectName", ignore = true)
	ProjectMilestoneVO toVO(ProjectMilestoneDO milestone);
	
	ProjectMilestoneDTO toDTO(ProjectMilestoneDO milestone);
	
	default ProjectMilestone fromVO(ProjectMilestoneVO vo) {
		ProjectMilestone res = ProjectMilestoneFactory.create();
		INSTANCE.update(vo, res);
		return res;
	}
	
	default ProjectMilestone fromDO(ProjectMilestoneDO resDO) {
		ProjectMilestone res = ProjectMilestoneFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "holidayJson", ignore = true)
	@Mapping(target = "milestoneMonthPlans", ignore = true)
	@Mapping(target = "projectInfo", ignore = true)
	@Mapping(target = "costPrice", ignore = true)
	@Mapping(target = "holiday", ignore = true)
	@Mapping(target = "monthWorkdays", ignore = true)
	void update(ProjectMilestoneDO source, @MappingTarget ProjectMilestone res);
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "createdTime", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "updatedTime", ignore = true)
	@Mapping(target = "holidayJson", ignore = true)
	@Mapping(target = "milestoneMonthPlans", ignore = true)
	@Mapping(target = "projectInfo", ignore = true)
	@Mapping(target = "costPrice", ignore = true)
	@Mapping(target = "holiday", ignore = true)
	@Mapping(target = "monthWorkdays", ignore = true)
	void update(ProjectMilestoneVO source, @MappingTarget ProjectMilestone res);
	
	default List<ProjectMilestoneDO> toDOs(List<ProjectMilestone> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(MilestoneDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
