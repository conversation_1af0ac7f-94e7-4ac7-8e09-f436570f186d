/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectWeekPlanRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.baseframe.utils.collect.MapUtils;
import com.swcares.pt.project.converter.WeekPlanDOConverter;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.dto.ProjectWeekPlanDTO;
import com.swcares.pt.project.service.ProjectWeekPlanService;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectWeekPlanRepositoryImpl <br>
 * Description：项目周计划仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectWeekPlanRepositoryImpl implements ProjectWeekPlanRepository {

	@Autowired
	private ProjectWeekPlanService weekPlanService;
	
	@Override
	public boolean save(ProjectWeekPlan data) {
		return weekPlanService.saveOrUpdate(WeekPlanDOConverter.INSTANCE.toDO(data));
	}

	@Override
	public boolean batchSave(List<ProjectWeekPlan> datas) {
		return weekPlanService.saveOrUpdateBatch(WeekPlanDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<ProjectWeekPlanVO> getByCondition(ProjectWeekPlanDTO params) {
		return weekPlanService.getByCondition(params);
	}

	@Override
	public List<ProjectWeekPlanVO> getWeekPlans(Integer year, Integer weekSerial, List<String> projectCodes, LocalDate startDate) {
		Map<String, Object> pcdoes = MapUtils.newHashMap();
		pcdoes.put("pcodes", projectCodes);
		ProjectWeekPlanDTO params = ProjectWeekPlanDTO.builder().year(year)
									.yearWeekNum(weekSerial)
									.weekNumGt(1)
									.startDate(startDate)
									.params(pcdoes).build();
		return getByCondition(params);
	}

}
