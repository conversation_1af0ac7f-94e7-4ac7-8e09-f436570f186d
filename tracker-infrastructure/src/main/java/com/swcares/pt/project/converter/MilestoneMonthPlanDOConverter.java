/**  
 * All rights Reserved, Designed By <br>
 * Title：StaffResourcesDOConverter.java <br>
 * Package：com.swcares.pt.base.data <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.domain.factory.MilestoneMonthPlanFactory;
import com.swcares.pt.project.entity.MilestoneMonthPlanDO;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.base.data.MilestoneMonthPlanDOConverter <br>
 * Description：里程碑月计划DO转换器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface MilestoneMonthPlanDOConverter {

	MilestoneMonthPlanDOConverter INSTANCE = Mappers.getMapper(MilestoneMonthPlanDOConverter.class);
	
	@Mapping(source = "user.username", target = "createdBy")
	@Mapping(source = "user.username", target = "updatedBy")
	MilestoneMonthPlanDO toDO(MilestoneMonthPlan res);
	
	@Mapping(target = "projectAlias", ignore = true)
	@Mapping(target = "projectName", ignore = true)
	ProjectMonthPlanVO toVO(MilestoneMonthPlanDO plan);
	
	default MilestoneMonthPlan fromDO(MilestoneMonthPlanDO resDO) {
		MilestoneMonthPlan res = MilestoneMonthPlanFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "monthPlanCode", ignore = true)
	@Mapping(target = "weekPlans", ignore = true)
	void update(MilestoneMonthPlanDO source, @MappingTarget MilestoneMonthPlan res);
	
	
	default List<MilestoneMonthPlanDO> toDOs(List<MilestoneMonthPlan> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(MilestoneMonthPlanDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
