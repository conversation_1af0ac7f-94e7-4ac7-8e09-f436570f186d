package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectMonthCostPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthCostDO;
import com.swcares.pt.project.mapper.ProjectMonthCostMapper;
import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.service.ProjectMonthCostService;
import com.swcares.pt.project.vo.ManagementUnitMonthStatVO;
import com.swcares.pt.project.vo.ProjectAnalysisStatVO;
import com.swcares.pt.project.vo.ProjectMonthCostVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectMonthCostServiceImpl <br>
 * Description：项目月成本 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectMonthCostServiceImpl extends BaseServiceImpl<ProjectMonthCostMapper, ProjectMonthCostDO>
		implements ProjectMonthCostService {

    @Override
    public IPage<ProjectMonthCostVO> page(ProjectMonthCostPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectMonthCostVO> getByCondition(QueryParam params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public List<ManagementUnitMonthStatVO> queryMgtUnitCostStat(LargeScreenParam param) {
		return baseMapper.selectMgtUnitCostStat(param);
	}

	@Override
	public List<ProjectAnalysisStatVO> queryCostAnalysis(LargeScreenParam param) {
		return baseMapper.selectCostAnalysis(param);
	}
	
}
