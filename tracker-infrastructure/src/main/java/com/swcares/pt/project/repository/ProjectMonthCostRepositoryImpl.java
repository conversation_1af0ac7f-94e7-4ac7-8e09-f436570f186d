/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthCostRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.pt.project.converter.MonthCostDOConverter;
import com.swcares.pt.project.domain.ProjectMonthCost;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.service.ProjectMonthCostService;
import com.swcares.pt.project.vo.ProjectMonthCostVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthCostRepositoryImpl <br>
 * Description：项目有成本仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectMonthCostRepositoryImpl implements ProjectMonthCostRepository {

	@Autowired
	private ProjectMonthCostService monthCostService;
	
	@Override
	public boolean save(ProjectMonthCost data) {
		return monthCostService.saveOrUpdate(MonthCostDOConverter.INSTANCE.toDO(data));
	}

	@Override
	public boolean batchSave(List<ProjectMonthCost> datas) {
		return monthCostService.saveOrUpdateBatch(MonthCostDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<ProjectMonthCostVO> getByCondition(QueryParam params) {
		return monthCostService.getByCondition(params);
	}

}
