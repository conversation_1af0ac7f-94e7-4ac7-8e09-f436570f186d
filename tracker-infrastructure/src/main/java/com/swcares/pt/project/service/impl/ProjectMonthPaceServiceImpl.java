package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectMonthPacePagedDTO;
import com.swcares.pt.project.entity.ProjectMonthPaceDO;
import com.swcares.pt.project.mapper.ProjectMonthPaceMapper;
import com.swcares.pt.project.param.LargeScreenParam;
import com.swcares.pt.project.param.QueryParam;
import com.swcares.pt.project.service.ProjectMonthPaceService;
import com.swcares.pt.project.vo.ManagementUnitMonthStatVO;
import com.swcares.pt.project.vo.ProjectAnalysisStatVO;
import com.swcares.pt.project.vo.ProjectMonthPaceVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectMonthPaceServiceImpl <br>
 * Description：项目月进度 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectMonthPaceServiceImpl
		extends BaseServiceImpl<ProjectMonthPaceMapper, ProjectMonthPaceDO>
		implements ProjectMonthPaceService {

    @Override
    public IPage<ProjectMonthPaceVO> page(ProjectMonthPacePagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectMonthPaceVO> getByCondition(QueryParam params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public List<ManagementUnitMonthStatVO> queryMgtUnitPaceStat(LargeScreenParam param) {
		return baseMapper.selectMgtUnitPaceStat(param);
	}

	@Override
	public List<ProjectAnalysisStatVO> queryPaceAnalysis(LargeScreenParam param) {
		return baseMapper.selectPaceAnalysis(param);
	}
	
}
