/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectYearStatRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.swcares.pt.project.converter.ProjectYearStatDOConverter;
import com.swcares.pt.project.domain.ProjectYearStat;
import com.swcares.pt.project.dto.ProjectYearStatDTO;
import com.swcares.pt.project.service.ProjectYearStatService;
import com.swcares.pt.project.vo.ProjectYearStatVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectYearStatRepositoryImpl <br>
 * Description：项目周计划仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectYearStatRepositoryImpl implements ProjectYearStatRepository {

	@Autowired
	private ProjectYearStatService yearStatService;
	
	@Override
	public boolean save(ProjectYearStat data) {
		return yearStatService.saveOrUpdate(ProjectYearStatDOConverter.INSTANCE.toDO(data));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchSave(List<ProjectYearStat> datas) {
		return yearStatService.saveOrUpdateBatch(ProjectYearStatDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<ProjectYearStatVO> getByCondition(ProjectYearStatDTO params) {
		return yearStatService.getByCondition(params);
	}

}
