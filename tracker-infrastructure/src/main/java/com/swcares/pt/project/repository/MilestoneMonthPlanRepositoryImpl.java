/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPlanRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.pt.project.converter.MilestoneMonthPlanDOConverter;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.dto.MilestoneMonthPlanDTO;
import com.swcares.pt.project.service.MilestoneMonthPlanService;
import com.swcares.pt.project.vo.MilestoneMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthPlanRepositoryImpl <br>
 * Description：里程碑月计划仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class MilestoneMonthPlanRepositoryImpl implements MilestoneMonthPlanRepository {

	@Autowired
	private MilestoneMonthPlanService monthPlanService;
	
	@Override
	public boolean save(MilestoneMonthPlan data) {
		return monthPlanService.saveOrUpdate(MilestoneMonthPlanDOConverter.INSTANCE.toDO(data));
	}

	@Override
	public boolean batchSave(List<MilestoneMonthPlan> datas) {
		return monthPlanService.saveOrUpdateBatch(MilestoneMonthPlanDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<MilestoneMonthPlanVO> getByCondition(MilestoneMonthPlanDTO params) {
		return monthPlanService.getByCondition(params);
	}

}
