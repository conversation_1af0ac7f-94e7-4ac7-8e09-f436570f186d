/**  
 * All rights Reserved, Designed By <br>
 * Title：WeeklyItemsDOConverter.java <br>
 * Package：com.swcares.pt.project.converter <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.WeeklyItems;
import com.swcares.pt.project.domain.factory.WeeklyItemsFactory;
import com.swcares.pt.project.entity.WeeklyItemsDO;
import com.swcares.pt.project.vo.WeeklyItemsVO;

/**   
 * ClassName：com.swcares.pt.project.converter.WeeklyItemsDOConverter <br>
 * Description：项目周报明细项DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface WeeklyItemsDOConverter {

	WeeklyItemsDOConverter INSTANCE = Mappers.getMapper(WeeklyItemsDOConverter.class);

	@Mapping(target = "deleted", ignore = true)
	WeeklyItemsDO toDO(WeeklyItems WeeklyItems);
	
	WeeklyItemsVO toVO(WeeklyItemsDO WeeklyItems);
	
	default WeeklyItems fromDO(WeeklyItemsDO resDO) {
		WeeklyItems res = WeeklyItemsFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	void update(WeeklyItemsDO source, @MappingTarget WeeklyItems res);
	
	default List<WeeklyItemsDO> toDOs(List<WeeklyItems> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(WeeklyItemsDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
