package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.dto.ProjectWeeklyPagedDTO;
import com.swcares.pt.project.entity.ProjectWeeklyDO;
import com.swcares.pt.project.mapper.ProjectWeeklyMapper;
import com.swcares.pt.project.param.ProjectWeeklyParam;
import com.swcares.pt.project.service.ProjectWeeklyService;
import com.swcares.pt.project.vo.ProjectWeeklyDetailVO;
import com.swcares.pt.project.vo.ProjectWeeklyVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectWeeklyServiceImpl <br>
 * Description：项目周报 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectWeeklyServiceImpl extends BaseServiceImpl<ProjectWeeklyMapper, ProjectWeeklyDO> implements ProjectWeeklyService {

    @Override
    public IPage<ProjectWeeklyVO> page(ProjectWeeklyPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectWeeklyVO> getByCondition(ProjectWeeklyParam params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public List<ProjectWeeklyDetailVO> getByConditionContainItems(ProjectWeeklyParam params) {
		return baseMapper.selectJoinWeeklyByCondition(params);
	}

	@Override
	public ProjectWeeklyDetailVO getByWeeklyId(Long id) {
		List<ProjectWeeklyDetailVO> weeklys = getByConditionContainItems(ProjectWeeklyParam.builder().id(id).build());
		if(ListUtils.isEmpty(weeklys)) {
			throw new BusinessException(CommonErrors.QUERY_ERROR);
		}
		return weeklys.get(0);
	}

}
