package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectMonthPlanDTO;
import com.swcares.pt.project.dto.ProjectMonthPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthPlanDO;
import com.swcares.pt.project.mapper.ProjectMonthPlanMapper;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.service.ProjectMonthPlanService;
import com.swcares.pt.project.vo.DeliverWorkLoadStatVO;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectMonthPlanServiceImpl <br>
 * Description：项目月计划 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectMonthPlanServiceImpl extends BaseServiceImpl<ProjectMonthPlanMapper, ProjectMonthPlanDO>
		implements ProjectMonthPlanService {

    @Override
    public IPage<ProjectMonthPlanVO> page(ProjectMonthPlanPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectMonthPlanVO> getByCondition(ProjectMonthPlanDTO params) {
		return baseMapper.selectByCondition(params);
	}

	@Override
	public List<DeliverWorkLoadStatVO> deliverWorkLoadStat(ProjectInfoParam params) {
		return baseMapper.deliverWorkLoadStat(params);
	}

	@Override
	public boolean removeByCondition(MilestoneDelParam params) {
		return baseMapper.removeByCondition(params) > 0;
	}
	
}
