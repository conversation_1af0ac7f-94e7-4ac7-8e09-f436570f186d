/**  
 * All rights Reserved, Designed By <br>
 * Title：WeekPlanDOConverter.java <br>
 * Package：com.swcares.pt.project.converter <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.domain.factory.ProjectWeekPlanFactory;
import com.swcares.pt.project.entity.ProjectWeekPlanDO;
import com.swcares.pt.project.vo.ProjectWeekPlanVO;

/**   
 * ClassName：com.swcares.pt.project.converter.WeekPlanDOConverter <br>
 * Description：周计划DO转换器 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:11:31 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface WeekPlanDOConverter {

	WeekPlanDOConverter INSTANCE = Mappers.getMapper(WeekPlanDOConverter.class);

	ProjectWeekPlanDO toDO(ProjectWeekPlan WeekPlan);
	
	ProjectWeekPlanVO toVO(ProjectWeekPlanDO WeekPlan);
	
	default ProjectWeekPlan fromDO(ProjectWeekPlanDO resDO) {
		ProjectWeekPlan res = ProjectWeekPlanFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	void update(ProjectWeekPlanDO source, @MappingTarget ProjectWeekPlan res);
	
	default List<ProjectWeekPlanDO> toDOs(List<ProjectWeekPlan> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(WeekPlanDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
