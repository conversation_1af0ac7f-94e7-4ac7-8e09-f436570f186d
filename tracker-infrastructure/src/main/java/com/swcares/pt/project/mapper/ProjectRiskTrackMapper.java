package com.swcares.pt.project.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import com.swcares.pt.project.dto.ProjectRiskTrackDTO;
import com.swcares.pt.project.dto.ProjectRiskTrackPagedDTO;
import com.swcares.pt.project.entity.ProjectRiskTrackDO;
import com.swcares.pt.project.vo.ProjectRiskTrackVO;

/**
 * ClassName：com.swcares.pt.project.pmt.mapper.ProjectRiskTrackMapper <br>
 * Description：项目风险 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
public interface ProjectRiskTrackMapper extends BaseMapper<ProjectRiskTrackDO> {

    /**
     * Description：分页查询 <br>
     * author：luojl <br>
     * date：2025-03-14 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<ProjectRiskTrackVO> page(@Param("p") ProjectRiskTrackPagedDTO dto, Page<ProjectRiskTrackVO> page);
    
    /**
     * Description：根据查询条件获取项目风险数据集 <br>
     * author：罗江林 <br>
     * date：2025年3月12日 下午3:25:42 <br>
     * @param params
     * @return <br>
     */
    List<ProjectRiskTrackVO> selectByCondition(@Param("p") ProjectRiskTrackDTO params);
    
}
