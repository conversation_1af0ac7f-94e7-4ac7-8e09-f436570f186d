package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectRiskTrackDTO;
import com.swcares.pt.project.dto.ProjectRiskTrackPagedDTO;
import com.swcares.pt.project.entity.ProjectRiskTrackDO;
import com.swcares.pt.project.mapper.ProjectRiskTrackMapper;
import com.swcares.pt.project.service.ProjectRiskTrackService;
import com.swcares.pt.project.vo.ProjectRiskTrackVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectRiskTrackServiceImpl <br>
 * Description：项目风险 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectRiskTrackServiceImpl extends BaseServiceImpl<ProjectRiskTrackMapper, ProjectRiskTrackDO> implements ProjectRiskTrackService {

    @Override
    public IPage<ProjectRiskTrackVO> page(ProjectRiskTrackPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectRiskTrackVO> getByCondition(ProjectRiskTrackDTO params) {
		return baseMapper.selectByCondition(params);
	}
	
}
