package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.WeeklyItemsDTO;
import com.swcares.pt.project.dto.WeeklyItemsPagedDTO;
import com.swcares.pt.project.entity.WeeklyItemsDO;
import com.swcares.pt.project.mapper.WeeklyItemsMapper;
import com.swcares.pt.project.service.WeeklyItemsService;
import com.swcares.pt.project.vo.WeeklyItemsVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.WeeklyItemsServiceImpl <br>
 * Description：周报项 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Service
public class WeeklyItemsServiceImpl extends BaseServiceImpl<WeeklyItemsMapper, WeeklyItemsDO> implements WeeklyItemsService {

    @Override
    public IPage<WeeklyItemsVO> page(WeeklyItemsPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<WeeklyItemsVO> getByCondition(WeeklyItemsDTO params) {
		return baseMapper.selectByCondition(params);
	}
	
}
