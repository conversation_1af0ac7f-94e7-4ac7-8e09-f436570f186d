package com.swcares.pt.project.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.project.dto.ProjectYearStatDTO;
import com.swcares.pt.project.dto.ProjectYearStatPagedDTO;
import com.swcares.pt.project.entity.ProjectYearStatDO;
import com.swcares.pt.project.mapper.ProjectYearStatMapper;
import com.swcares.pt.project.service.ProjectYearStatService;
import com.swcares.pt.project.vo.ProjectYearStatVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.impl.ProjectYearStatServiceImpl <br>
 * Description：项目年统计 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
@Service
public class ProjectYearStatServiceImpl extends BaseServiceImpl<ProjectYearStatMapper, ProjectYearStatDO> implements ProjectYearStatService {

    @Override
    public IPage<ProjectYearStatVO> page(ProjectYearStatPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

	@Override
	public List<ProjectYearStatVO> getByCondition(ProjectYearStatDTO params) {
		return baseMapper.selectByCondition(params);
	}
    
    
}
