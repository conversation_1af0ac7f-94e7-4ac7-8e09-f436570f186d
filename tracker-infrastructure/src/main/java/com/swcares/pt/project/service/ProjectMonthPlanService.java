package com.swcares.pt.project.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectMonthPlanDTO;
import com.swcares.pt.project.dto.ProjectMonthPlanPagedDTO;
import com.swcares.pt.project.entity.ProjectMonthPlanDO;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.param.ProjectInfoParam;
import com.swcares.pt.project.vo.DeliverWorkLoadStatVO;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectMonthPlanService <br>
 * Description：项目月计划 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024-08-15 <br>
 * @version v1.0 <br>
 */
public interface ProjectMonthPlanService extends BaseService<ProjectMonthPlanDO> {

    /**
     * Description：分页查询 <br>
     * author：luojl <br>
     * date：2024-08-15 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectMonthPlanVO> page(ProjectMonthPlanPagedDTO dto);
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectMonthPlanVO> getByCondition(ProjectMonthPlanDTO params);
    
    /**
     * Description：交付工量需求，季度统计 <br>
     * author：罗江林 <br>
     * date：2024年8月20日 下午2:18:42 <br>
     * @return <br>
     */
    List<DeliverWorkLoadStatVO> deliverWorkLoadStat(ProjectInfoParam params);
    
    /**
     * Description：根据里程碑、月计划、项目编号进行物理删除 <br>
     * author：罗江林 <br>
     * date：2025年3月17日 下午5:20:07 <br>
     * @param params
     * @return <br>
     */
    boolean removeByCondition(MilestoneDelParam params);
    
}
