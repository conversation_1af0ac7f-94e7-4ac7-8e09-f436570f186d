/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMonthPlanRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.pt.project.converter.MonthPlanDOConverter;
import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.dto.ProjectMonthPlanDTO;
import com.swcares.pt.project.service.ProjectMonthPlanService;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMonthPlanRepositoryImpl <br>
 * Description：项目月计划仓储数据库存储实现 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月15日 下午3:34:34 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectMonthPlanRepositoryImpl implements ProjectMonthPlanRepository {

	@Autowired
	private ProjectMonthPlanService monthPlanService;
	
	@Override
	public boolean save(ProjectMonthPlan data) {
		return monthPlanService.saveOrUpdate(MonthPlanDOConverter.INSTANCE.toDO(data));
	}

	@Override
	public boolean batchSave(List<ProjectMonthPlan> datas) {
		return monthPlanService.saveOrUpdateBatch(MonthPlanDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	}

	@Override
	public List<ProjectMonthPlanVO> getByCondition(ProjectMonthPlanDTO params) {
		return monthPlanService.getByCondition(params);
	}

}
