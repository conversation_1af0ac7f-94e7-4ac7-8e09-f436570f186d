/**  
 * All rights Reserved, Designed By <br>
 * Title：StaffResourcesDOConverter.java <br>
 * Package：com.swcares.pt.base.data <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.domain.factory.ProjectMonthPlanFactory;
import com.swcares.pt.project.entity.ProjectMonthPlanDO;
import com.swcares.pt.project.vo.ProjectMonthPlanVO;

/**   
 * ClassName：com.swcares.pt.base.data.MonthPlanDOConverter <br>
 * Description：项目月计划DO转换器 <br>
 * Copyright © 2024 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2024年8月13日 下午2:27:37 <br>
 * @version v1.0 <br>  
 */
@Mapper
public interface MonthPlanDOConverter {

	MonthPlanDOConverter INSTANCE = Mappers.getMapper(MonthPlanDOConverter.class);
	
	@Mapping(source = "user.username", target = "createdBy")
	@Mapping(source = "user.username", target = "updatedBy")
	ProjectMonthPlanDO toDO(ProjectMonthPlan res);
	
	@Mapping(target = "projectAlias", ignore = true)
	@Mapping(target = "projectName", ignore = true)
	ProjectMonthPlanVO toVO(ProjectMonthPlanDO plan);
	
	default ProjectMonthPlan fromDO(ProjectMonthPlanDO resDO) {
		ProjectMonthPlan res = ProjectMonthPlanFactory.create();
		INSTANCE.update(resDO, res);
		return res;
	}
	
	@Mapping(target = "repository", ignore = true)
	@Mapping(target = "user", ignore = true)
	@Mapping(target = "weekPlans", ignore = true)
	void update(ProjectMonthPlanDO source, @MappingTarget ProjectMonthPlan res);
	
	
	default List<ProjectMonthPlanDO> toDOs(List<ProjectMonthPlan> datas){
		if(ListUtils.isEmpty(datas)) {
			return ListUtils.newArrayList();
		}
		return datas.stream().map(MonthPlanDOConverter.INSTANCE::toDO).collect(Collectors.toList());
	}
	
}
