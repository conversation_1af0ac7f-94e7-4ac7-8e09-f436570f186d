/**  
 * All rights Reserved, Designed By <br>
 * Title：ProjectMilestoneRepositoryImpl.java <br>
 * Package：com.swcares.pt.project.repository <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:09:02 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.pt.project.repository;

import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.collect.SetUtils;
import com.swcares.pt.project.converter.MilestoneDOConverter;
import com.swcares.pt.project.converter.MilestoneMonthPlanDOConverter;
import com.swcares.pt.project.converter.MonthPlanDOConverter;
import com.swcares.pt.project.converter.WeekPlanDOConverter;
import com.swcares.pt.project.domain.MilestoneMonthPlan;
import com.swcares.pt.project.domain.ProjectMilestone;
import com.swcares.pt.project.domain.ProjectMonthPlan;
import com.swcares.pt.project.domain.ProjectWeekPlan;
import com.swcares.pt.project.domain.service.ProjectYearStatWrapper;
import com.swcares.pt.project.dto.ProjectMilestoneDTO;
import com.swcares.pt.project.param.MilestoneDelParam;
import com.swcares.pt.project.service.MilestoneMonthPlanService;
import com.swcares.pt.project.service.ProjectMilestoneService;
import com.swcares.pt.project.service.ProjectMonthPlanService;
import com.swcares.pt.project.service.ProjectWeekPlanService;
import com.swcares.pt.project.vo.ProjectMilestoneVO;

/**   
 * ClassName：com.swcares.pt.project.repository.ProjectMilestoneRepositoryImpl <br>
 * Description：里程碑仓储接口数据库实现 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年3月12日 下午3:09:02 <br>
 * @version v1.0 <br>  
 */
@Service
public class ProjectMilestoneRepositoryImpl implements ProjectMilestoneRepository {

	@Autowired
	private ProjectMilestoneService milestoneService;
	@Autowired
	private MilestoneMonthPlanService milestoneMonthPlanService;
	@Autowired
	private ProjectMonthPlanService projectMonthPlanService;
	@Autowired
	private ProjectWeekPlanService weekPlanService;
	@Autowired
	private ProjectYearStatRepository yearStateRepository;
	@Autowired
	private ProjectYearStatWrapper statWrapper;
	
	@Override
	public boolean save(ProjectMilestone data) {
		return milestoneService.saveOrUpdate(MilestoneDOConverter.INSTANCE.toDO(data));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchSave(List<ProjectMilestone> datas) {
		removeDatas(datas);
		List<MilestoneMonthPlan> mps = gainMilestoneMonthPlan(datas);
		List<ProjectMonthPlan> pmps = gainProjectMonthPlan(datas);
		List<ProjectWeekPlan> wps = gainPorjectWeekPlans(mps);
		weekPlanService.saveOrUpdateBatch(WeekPlanDOConverter.INSTANCE.toDOs(wps), BATCH_SIZE);
		projectMonthPlanService.saveOrUpdateBatch(MonthPlanDOConverter.INSTANCE.toDOs(pmps), BATCH_SIZE);
		milestoneMonthPlanService.saveOrUpdateBatch(MilestoneMonthPlanDOConverter.INSTANCE.toDOs(mps), BATCH_SIZE);
		yearStateRepository.batchSave(statWrapper.applyProjectYearStat(datas));
		return milestoneService.saveOrUpdateBatch(MilestoneDOConverter.INSTANCE.toDOs(datas), BATCH_SIZE);
	} 
	
	private void removeDatas(List<ProjectMilestone> datas) {
		Set<String> codes = SetUtils.newHashSet();
		datas.forEach(d -> { codes.add(d.getProjectCode()); });
		MilestoneDelParam param = MilestoneDelParam.builder().pCodes(ListUtils.newArrayList(codes)).build();
		projectMonthPlanService.removeByCondition(param);
		milestoneMonthPlanService.removeByCondition(param);
		weekPlanService.removeByCondition(param);
	}
	
	private List<ProjectWeekPlan> gainPorjectWeekPlans(List<MilestoneMonthPlan> datas){
		List<ProjectWeekPlan> wps = ListUtils.newArrayList();
		datas.forEach(p -> { 
			wps.addAll(p.getWeekPlans());
		});
		return wps;
	}
	
	private List<ProjectMonthPlan> gainProjectMonthPlan(List<ProjectMilestone> datas){
		List<ProjectMonthPlan> mps = ListUtils.newArrayList();
		for (ProjectMilestone pm : datas) {
			List<ProjectMonthPlan> psp = pm.getProjectMonthPlans();
			if(ListUtils.isNotEmpty(psp)) {
				mps.addAll(psp);
			}
		} 
		return mps;
	}
	
	private List<MilestoneMonthPlan> gainMilestoneMonthPlan(List<ProjectMilestone> datas){
		List<MilestoneMonthPlan> mps = ListUtils.newArrayList();
		datas.forEach(m -> {
			mps.addAll(m.getMilestoneMonthPlans());
		});
		return mps;
	}

	@Override
	public List<ProjectMilestoneVO> getByCondition(ProjectMilestoneDTO params) {
		return milestoneService.getByCondition(params);
	}

	@Override
	public boolean remove(ProjectMilestone p) {
		return milestoneService.removeByCondition(ProjectMilestoneDTO.builder().milestoneCode(p.getMilestoneCode()).build());
	}
	

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean delMilestoneAndMonthPlanWeekPlan(ProjectMilestone p) {
		milestoneMonthPlanService.removeByCondition(MilestoneDelParam.builder().mCodes(ListUtils.newArrayList(p.getMilestoneCode())).build());
		weekPlanService.removeByCondition(MilestoneDelParam.builder().mCodes(ListUtils.newArrayList(p.getMilestoneCode())).build());
		return remove(p);
	}
	
	@Override
	public List<ProjectMilestoneVO> getByProjectState(ProjectMilestoneDTO params) {
		return milestoneService.getByProjectState(params);
	}

	@Override
	public ProjectMilestoneVO getByMilestoneCode(String milestoneCode) {
		return milestoneService.getByMilestoneCode(milestoneCode);
	}

	
}
