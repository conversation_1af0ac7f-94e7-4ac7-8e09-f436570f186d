package com.swcares.pt.project.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.project.dto.ProjectYearStatDTO;
import com.swcares.pt.project.dto.ProjectYearStatPagedDTO;
import com.swcares.pt.project.entity.ProjectYearStatDO;
import com.swcares.pt.project.vo.ProjectYearStatVO;

/**
 * ClassName：com.swcares.pt.project.pmt.service.ProjectYearStatService <br>
 * Description：项目年统计 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-03-14 <br>
 * @version v1.0 <br>
 */
public interface ProjectYearStatService extends BaseService<ProjectYearStatDO> {

    /**
     * Description：项目年统计分页查询 <br>
     * author：luojl <br>
     * date：2025-03-14 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<ProjectYearStatVO> page(ProjectYearStatPagedDTO dto);
    
    /**
     * Description：根据查询条件获取数据集 <br>
     * author：罗江林 <br>
     * date：2024年8月15日 下午2:32:39 <br>
     * @param params
     * @return <br>
     */
    List<ProjectYearStatVO> getByCondition(ProjectYearStatDTO params);
    
    
}
