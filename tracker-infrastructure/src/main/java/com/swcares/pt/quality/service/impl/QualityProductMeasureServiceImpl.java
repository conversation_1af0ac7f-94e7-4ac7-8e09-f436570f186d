package com.swcares.pt.quality.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.entity.QualityProductMeasureDO;
import com.swcares.pt.quality.mapper.QualityProductMeasureMapper;
import com.swcares.pt.quality.service.QualityProductMeasureService;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;

/**
 * ClassName：com.swcares.pt.quality.service.impl.QualityProductMeasureServiceImpl <br>
 * Description：产品质量度量 服务实现类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
@Service
public class QualityProductMeasureServiceImpl extends BaseServiceImpl<QualityProductMeasureMapper, QualityProductMeasureDO>
        implements QualityProductMeasureService {

    @Override
    public IPage<QualityProductMeasureVO> page(QualityProductMeasurePagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    @Override
    public List<QualityProductMeasureVO> getByCondition(QualityProductMeasureDTO params) {
        return baseMapper.selectByCondition(params);
    }

}
