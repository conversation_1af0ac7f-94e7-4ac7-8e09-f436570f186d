package com.swcares.pt.quality.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.swcares.pt.quality.converter.QualityProductMeasureDOConverter;
import com.swcares.pt.quality.domain.QualityProductMeasure;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.service.QualityProductMeasureService;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;
import com.swcares.baseframe.utils.collect.ListUtils;

/**
 * ClassName：com.swcares.pt.quality.repository.QualityProductMeasureRepositoryImpl <br>
 * Description：产品质量度量仓储数据库存储实现 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * <AUTHOR> <br>
 * date 2025年7月2日 <br>
 * @version v1.0 <br>
 */
@Service
public class QualityProductMeasureRepositoryImpl implements QualityProductMeasureRepository {

    @Autowired
    private QualityProductMeasureService qualityProductMeasureService;

    @Override
    public boolean save(QualityProductMeasure qpm) {
        return qualityProductMeasureService.saveOrUpdate(QualityProductMeasureDOConverter.INSTANCE.toDO(qpm));
    }

    @Override
    public boolean batchSave(List<QualityProductMeasure> qpms) {
        return qualityProductMeasureService.saveOrUpdateBatch(QualityProductMeasureDOConverter.INSTANCE.toDOs(qpms), BATCH_SIZE);
    }

    @Override
    public List<QualityProductMeasureVO> getByCondition(QualityProductMeasureDTO params) {
        return qualityProductMeasureService.getByCondition(params);
    }

    @Override
    public QualityProductMeasureVO getLatestByProductCode(String productCode) {
        QualityProductMeasureDTO params = QualityProductMeasureDTO.builder()
                .productCode(productCode)
                .build();
        List<QualityProductMeasureVO> qpms = getByCondition(params);
        if (ListUtils.isNotEmpty(qpms)) {
            // 返回最新的记录（按统计时间排序，第一条即为最新）
            return qpms.get(0);
        }
        return null;
    }

}
