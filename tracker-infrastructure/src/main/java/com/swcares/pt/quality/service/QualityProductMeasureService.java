package com.swcares.pt.quality.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.baseframe.common.mybatis.base.BaseService;
import com.swcares.pt.quality.dto.QualityProductMeasureDTO;
import com.swcares.pt.quality.dto.QualityProductMeasurePagedDTO;
import com.swcares.pt.quality.entity.QualityProductMeasureDO;
import com.swcares.pt.quality.vo.QualityProductMeasureVO;

/**
 * ClassName：com.swcares.pt.quality.service.QualityProductMeasureService <br>
 * Description：产品质量度量 服务类 <br>
 * Copyright © 2025 luojl All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2025-07-02 <br>
 * @version v1.0 <br>
 */
public interface QualityProductMeasureService extends BaseService<QualityProductMeasureDO> {

    /**
     * Description：分页查询产品质量度量 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param dto 查询条件
     * @return 分页结果 <br>
     */
    IPage<QualityProductMeasureVO> page(QualityProductMeasurePagedDTO dto);

    /**
     * Description：根据条件查询产品质量度量列表 <br>
     * author：luojl <br>
     * date：2025年7月2日 <br>
     * @param params 查询条件
     * @return 质量度量列表 <br>
     */
    List<QualityProductMeasureVO> getByCondition(QualityProductMeasureDTO params);

}
