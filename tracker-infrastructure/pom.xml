<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.swcares.pt</groupId>
		<artifactId>project-tracker</artifactId>
		<version>1.1.0-SNAPSHOT</version>
	</parent>
	<artifactId>tracker-infrastructure</artifactId>
	
	<dependencies>
		<dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>base-frame-starter</artifactId>
        </dependency> 
        
        <dependency>
            <groupId>com.swcares.pt</groupId>
            <artifactId>tracker-aggregation</artifactId>
        </dependency>
        
        <dependency>
		    <groupId>org.gitlab4j</groupId>
		    <artifactId>gitlab4j-api</artifactId>
		</dependency>
        
        <dependency>
			<groupId>com.swcares.tools</groupId>
			<artifactId>system-code-generator</artifactId>
		</dependency>
		
		<!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <scope>provided</scope>
        </dependency>
        
    </dependencies>
    
    <build>
		<!-- 资源文件配置 -->
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
	</build>
	
</project>